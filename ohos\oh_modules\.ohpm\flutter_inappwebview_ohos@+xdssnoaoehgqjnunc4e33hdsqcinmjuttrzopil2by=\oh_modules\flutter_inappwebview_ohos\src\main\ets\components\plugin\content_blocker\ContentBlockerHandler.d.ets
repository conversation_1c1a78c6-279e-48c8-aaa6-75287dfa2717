// @keepTs
// @ts-nocheck
import ContentBlocker from './ContentBlocker';
import ArrayList from "@ohos.util.ArrayList";
import InAppWebView from '../webview/in_app_webview/InAppWebView';
import WebResourceRequestExt from '../types/WebResourceRequestExt';
import { ContentBlockerTriggerResourceType } from './ContentBlockerTriggerResourceType';
export default class ContentBlockerHandler {
    protected ruleList: ArrayList<ContentBlocker>;
    constructor(o14?: ArrayList<ContentBlocker>);
    getRuleList(): ArrayList<ContentBlocker>;
    setRuleList(n14: ArrayList<ContentBlocker>): void;
    checkUrl(k14: InAppWebView, l14: WebResourceRequestExt, m14?: string): WebResourceResponse | null;
    checkUrlWithResourceType(h14: InAppWebView, i14: WebResourceRequestExt, j14: ContentBlockerTriggerResourceType): WebResourceResponse | null;
    getResourceTypeFromContentType(g14: string): ContentBlockerTriggerResourceType;
}
