// @keepTs
// @ts-nocheck
import web_webview from '@ohos.web.webview';
import { Any } from '@ohos/flutter_ohos';
export default class HitTestResult {
    private type;
    private extra;
    private static unknownType;
    private static phoneType;
    private static geoType;
    private static emailType;
    private static imageType;
    private static SRC_ANCHOR_TYPE;
    private static srcImageAnchorType;
    private static editTextType;
    constructor(m18: number, n18: string | null);
    static getFlutterHitTestResultType(l18: web_webview.WebHitTestType): number;
    static fromWebViewHitTestResult(k18: web_webview.HitTestValue | null): HitTestResult | null;
    getType(): number;
    setType(j18: number): void;
    getExtra(): string | null;
    setExtra(i18: string | null): void;
    toMap(): Map<string, Any>;
    equals(h18: Any): boolean;
    hashCode(): number;
    toString(): string;
}
