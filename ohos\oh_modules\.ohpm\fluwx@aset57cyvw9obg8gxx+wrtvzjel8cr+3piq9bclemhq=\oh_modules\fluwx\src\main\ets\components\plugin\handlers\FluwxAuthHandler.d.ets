// @keepTs
// @ts-nocheck
import { <PERSON><PERSON>all, MethodChannel, MethodResult } from "@ohos/flutter_ohos";
import { IDiffDevOAuth, OAuthCallback, OAuthErrCode } from "@tencent/wechat_open_sdk";
export declare class FluwxAuthHandler implements OAuthCallback {
    private channel;
    private diffDevOauth;
    constructor(f: MethodChannel);
    onGotQRCode: (base64JpegImageBuffer: string) => void;
    onQRCodeScanned: () => void;
    onAuthFinish: (authCode: string) => void;
    onAuthError: (errCode: OAuthErrCode, errMsg: string) => void;
    getDiffDevOauth(): IDiffDevOAuth;
    sendAuth(d: Method<PERSON>all, e: MethodResult): Promise<void>;
    authByQRCode(b: MethodCall, c: MethodResult): void;
    stopAuthByQRCode(a: MethodResult): void;
}
