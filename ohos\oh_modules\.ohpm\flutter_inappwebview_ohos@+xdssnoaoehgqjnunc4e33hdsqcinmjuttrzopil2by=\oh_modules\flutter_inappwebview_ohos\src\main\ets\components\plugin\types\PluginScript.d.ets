// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
import ContentWorld from './ContentWorld';
import UserScript from './UserScript';
import { UserScriptInjectionTime } from './UserScriptInjectionTime';
export default class PluginScript extends UserScript {
    private requiredInAllContentWorlds;
    constructor(z12: string, a13: string, b13: UserScriptInjectionTime, c13: ContentWorld | null, d13: boolean, e13: Set<string> | null);
    isRequiredInAllContentWorlds(): boolean;
    setRequiredInAllContentWorlds(y12: boolean): void;
    equals(x12: Any): boolean;
    hashCode(): number;
    toString(): string;
}
