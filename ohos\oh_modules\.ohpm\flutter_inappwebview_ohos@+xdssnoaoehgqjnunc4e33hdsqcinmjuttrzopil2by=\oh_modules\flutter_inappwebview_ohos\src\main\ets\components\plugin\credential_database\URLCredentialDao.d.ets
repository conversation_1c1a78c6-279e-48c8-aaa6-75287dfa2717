// @keepTs
// @ts-nocheck
import URLCredential from '../types/URLCredential';
import CredentialDatabaseHelper from './CredentialDatabaseHelper';
import List from '@ohos.util.List';
export default class URLCredentialDao {
    credentialDatabaseHelper: CredentialDatabaseHelper;
    projection: string[];
    constructor(d29: CredentialDatabaseHelper);
    getAllByProtectionSpaceId(c29: number): Promise<List<URLCredential>>;
    find(z28: string, a29: string, b29: number): Promise<URLCredential>;
    insert(y28: URLCredential): Promise<number>;
    update(x28: URLCredential): Promise<number>;
    delete(w28: URLCredential): Promise<number>;
}
