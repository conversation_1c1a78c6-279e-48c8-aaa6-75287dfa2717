// @keepTs
// @ts-nocheck
import { FlutterPlugin, FlutterPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import { <PERSON><PERSON><PERSON><PERSON>and<PERSON>, MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import MethodCall from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodCall';
import { AbilityAware, AbilityPluginBinding } from '@ohos/flutter_ohos/index';
import { Barcode } from './Barcode';
import { BusinessError as BusinessError } from "@ohos.base";
export declare class MobileScannerPlugin implements FlutterPlugin, MethodCallHandler, AbilityAware {
    getUniqueClassName(): string;
    onAttachedToAbility(j1: AbilityPluginBinding): void;
    onDetachedFromAbility(): void;
    private methodChannel;
    private eventChannel;
    private eventSink;
    private applicationContext;
    private ability;
    private textureId;
    private surfaceId;
    private binding;
    private isStart;
    private scanWidth;
    private scanHeight;
    private returnImage;
    private cameraResolutionValues;
    private resolutionRatio;
    private torch;
    private facing;
    private formats;
    private speed;
    private timeout;
    private useNewCameraSelector;
    private options;
    private retryScanTimes;
    private scanWindow;
    private imageBuffer;
    publishEvent(i1: ESObject): void;
    onAttachedToEngine(h1: FlutterPluginBinding): void;
    onDetachedFromEngine(g1: FlutterPluginBinding): void;
    onMethodCall(e1: MethodCall, f1: MethodResult): void;
    start(c1: MethodCall, d1: MethodResult): Promise<void>;
    retryCamera(b1: BusinessError): void;
    startCamera(): void;
    private scanCallback;
    private frameCallback;
    startScan(): void;
    setDisplay(): void;
    toDoubleType(a1: number): number;
    stop(z: MethodResult | null): Promise<void>;
    toggleTorch(x: MethodCall, y: MethodResult): void;
    analyzeImage(v: MethodCall, w: MethodResult): void;
    setScale(t: MethodCall, u: MethodResult): void;
    resetScale(s: MethodResult): void;
    updateScanWindow(q: MethodCall, r: MethodResult): void;
    callback(m: Barcode[], n: Uint8Array | null, o: number, p: number): void;
    private isBarcodeInScanWindow;
    errorCallback(l: string): void;
    torchCallback(j: BusinessError, k: boolean): void;
}
