// @keepTs
// @ts-nocheck
import media from '@ohos.multimedia.media';
export declare function wrapError(u1: Error): Array<Object>;
export declare class CreateMessage {
    constructor();
    private asset;
    getAsset(): string;
    setAsset(t1: string): void;
    private uri;
    getUri(): string;
    setUri(s1: string): void;
    private packageName;
    getPackageName(): String;
    setPackageName(r1: String): void;
    private formatHint;
    getFormatHint(): String;
    setFormatHint(q1: String): void;
    private httpHeaders;
    getHttpHeaders(): Map<String, String>;
    setHttpHeaders(p1: Map<String, String>): void;
    toList(): Array<Object>;
    static fromList(m1: Array<Object>): CreateMessage;
}
export declare class LoopingMessage {
    constructor();
    private textureId;
    getTextureId(): Number;
    setTextureId(l1: Number): void;
    private isLooping;
    getIsLooping(): boolean;
    setIsLooping(k1: boolean): void;
    toList(): Array<Object>;
    static fromList(f1: Array<Object>): LoopingMessage;
}
export declare class MixWithOthersMessage {
    constructor();
    private mixWithOthers;
    getMixWithOthers(): Boolean;
    setMixWithOthers(e1: Boolean): void;
    toList(): Array<Object>;
    static fromList(a1: Array<Object>): MixWithOthersMessage;
}
export declare class PlaybackSpeedMessage {
    constructor();
    private textureId;
    getTextureId(): Number;
    setTextureId(z: Number): void;
    private speed;
    getSpeed(): media.PlaybackSpeed | null;
    setSpeed(y: Number): void;
    toList(): Array<Object>;
    static fromList(t: Array<Object>): PlaybackSpeedMessage;
}
export declare class PositionMessage {
    constructor();
    private textureId;
    getTextureId(): Number;
    setTextureId(s: Number): void;
    private position;
    getPosition(): Number;
    setPosition(r: Number): void;
    toList(): Array<Object>;
    static fromList(m: Array<Object>): PositionMessage;
}
export declare class TextureMessage {
    private textureId;
    getTextureId(): Number;
    setTextureId(l: Number): void;
    toList(): Array<Object>;
    static fromList(h: Array<Object>): TextureMessage;
}
export declare class VolumeMessage {
    constructor();
    private textureId;
    getTextureId(): Number;
    setTextureId(g: Number): void;
    private volume;
    getVolume(): Number;
    setVolume(f: Number): void;
    toList(): Array<Object>;
    static fromList(a: Array<Object>): VolumeMessage;
}
