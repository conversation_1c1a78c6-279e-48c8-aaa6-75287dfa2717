// @keepTs
// @ts-nocheck
import PluginScript from '../types/PluginScript';
export default class PluginScriptsUtil {
    static VAR_PLACEHOLDER_VALUE: string;
    static VAR_CONTENT_WORLD_NAME_ARRAY: string;
    static VAR_CONTENT_WORLD_NAME: string;
    static VAR_JSON_SOURCE_ENCODED: string;
    static VAR_FUNCTION_ARGUMENT_NAMES: string;
    static VAR_FUNCTION_ARGUMENT_VALUES: string;
    static VAR_FUNCTION_ARGUMENTS_OBJ: string;
    static VAR_FUNCTION_BODY: string;
    static VAR_RESULT_UUID: string;
    static VAR_RANDOM_NAME: string;
    static CALL_ASYNC_JAVA_SCRIPT_WRAPPER_JS_SOURCE: string;
    static EVALUATE_JAVASCRIPT_WITH_CONTENT_WORLD_WRAPPER_JS_SOURCE: string;
    static IS_ACTIVE_ELEMENT_INPUT_EDITABLE_JS_SOURCE: string;
    static CHECK_CONTEXT_MENU_SHOULD_BE_HIDDEN_JS_SOURCE: string;
    static GET_SELECTED_TEXT_JS_SOURCE: string;
    static CHECK_GLOBAL_KEY_DOWN_EVENT_TO_HIDE_CONTEXT_MENU_JS_PLUGIN_SCRIPT_GROUP_NAME: string;
    static CHECK_GLOBAL_KEY_DOWN_EVENT_TO_HIDE_CONTEXT_MENU_JS_SOURCE: string;
    static CHECK_GLOBAL_KEY_DOWN_EVENT_TO_HIDE_CONTEXT_MENU_JS_PLUGIN_SCRIPT: PluginScript;
}
