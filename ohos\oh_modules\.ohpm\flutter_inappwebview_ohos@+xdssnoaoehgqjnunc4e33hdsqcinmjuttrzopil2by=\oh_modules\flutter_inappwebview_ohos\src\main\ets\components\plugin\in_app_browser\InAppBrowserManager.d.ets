// @keepTs
// @ts-nocheck
import InAppWebViewFlutterPlugin from '../InAppWebViewFlutterPlugin';
import ChannelDelegateImpl from '../types/ChannelDelegateImpl';
import { Any, MethodCall } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import UIAbility from "@ohos.app.ability.UIAbility";
export default class InAppBrowserManager extends ChannelDelegateImpl {
    plugin: InAppWebViewFlutterPlugin | null;
    id: string;
    static shared: Map<string, InAppBrowserManager>;
    constructor(t1: InAppWebViewFlutterPlugin);
    onMethodCall(r1: MethodCall, s1: MethodResult): void;
    open(p1: UIAbility, q1: Map<string, Any>): void;
    openWithSystemBrowser(m1: UIAbility, n1: string, o1: MethodResult): void;
    dispose(): void;
}
