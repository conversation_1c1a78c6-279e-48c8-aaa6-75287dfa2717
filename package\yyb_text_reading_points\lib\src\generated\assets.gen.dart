/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/bottom_btn_next_icon.png
  AssetGenImage get bottomBtnNextIcon =>
      const AssetGenImage('assets/images/bottom_btn_next_icon.png');

  /// File path: assets/images/bottom_btn_original_sound.png
  AssetGenImage get bottomBtnOriginalSound =>
      const AssetGenImage('assets/images/bottom_btn_original_sound.png');

  /// File path: assets/images/bottom_btn_original_sound_play1.png
  AssetGenImage get bottomBtnOriginalSoundPlay1 =>
      const AssetGenImage('assets/images/bottom_btn_original_sound_play1.png');

  /// File path: assets/images/bottom_btn_original_sound_play2.png
  AssetGenImage get bottomBtnOriginalSoundPlay2 =>
      const AssetGenImage('assets/images/bottom_btn_original_sound_play2.png');

  /// File path: assets/images/bottom_btn_original_sound_play3.png
  AssetGenImage get bottomBtnOriginalSoundPlay3 =>
      const AssetGenImage('assets/images/bottom_btn_original_sound_play3.png');

  /// File path: assets/images/bottom_btn_replay_icon.png
  AssetGenImage get bottomBtnReplayIcon =>
      const AssetGenImage('assets/images/bottom_btn_replay_icon.png');

  /// File path: assets/images/bottom_btn_replay_icon1.png
  AssetGenImage get bottomBtnReplayIcon1 =>
      const AssetGenImage('assets/images/bottom_btn_replay_icon1.png');

  /// File path: assets/images/bottom_btn_replay_icon2.png
  AssetGenImage get bottomBtnReplayIcon2 =>
      const AssetGenImage('assets/images/bottom_btn_replay_icon2.png');

  /// File path: assets/images/bottom_btn_reset_icon.png
  AssetGenImage get bottomBtnResetIcon =>
      const AssetGenImage('assets/images/bottom_btn_reset_icon.png');

  /// File path: assets/images/bottom_btn_submit_icon.png
  AssetGenImage get bottomBtnSubmitIcon =>
      const AssetGenImage('assets/images/bottom_btn_submit_icon.png');

  /// File path: assets/images/bottom_btn_un_next_icon.png
  AssetGenImage get bottomBtnUnNextIcon =>
      const AssetGenImage('assets/images/bottom_btn_un_next_icon.png');

  /// File path: assets/images/bottom_btn_un_replay_icon.png
  AssetGenImage get bottomBtnUnReplayIcon =>
      const AssetGenImage('assets/images/bottom_btn_un_replay_icon.png');

  /// File path: assets/images/bottom_btn_un_reset_icon.png
  AssetGenImage get bottomBtnUnResetIcon =>
      const AssetGenImage('assets/images/bottom_btn_un_reset_icon.png');

  /// File path: assets/images/bottom_btn_un_submit_icon.png
  AssetGenImage get bottomBtnUnSubmitIcon =>
      const AssetGenImage('assets/images/bottom_btn_un_submit_icon.png');

  /// File path: assets/images/chinese_arrow_icon.png
  AssetGenImage get chineseArrowIcon =>
      const AssetGenImage('assets/images/chinese_arrow_icon.png');

  /// File path: assets/images/chinese_back_icon.png
  AssetGenImage get chineseBackIcon =>
      const AssetGenImage('assets/images/chinese_back_icon.png');

  /// File path: assets/images/chinese_box_nor.png
  AssetGenImage get chineseBoxNor =>
      const AssetGenImage('assets/images/chinese_box_nor.png');

  /// File path: assets/images/chinese_box_sel.png
  AssetGenImage get chineseBoxSel =>
      const AssetGenImage('assets/images/chinese_box_sel.png');

  /// File path: assets/images/chinese_box_un_sel.png
  AssetGenImage get chineseBoxUnSel =>
      const AssetGenImage('assets/images/chinese_box_un_sel.png');

  /// File path: assets/images/chinese_buttom_bg.png
  AssetGenImage get chineseButtomBg =>
      const AssetGenImage('assets/images/chinese_buttom_bg.png');

  /// File path: assets/images/chinese_describe_content.png
  AssetGenImage get chineseDescribeContent =>
      const AssetGenImage('assets/images/chinese_describe_content.png');

  /// File path: assets/images/chinese_describe_top.png
  AssetGenImage get chineseDescribeTop =>
      const AssetGenImage('assets/images/chinese_describe_top.png');

  /// File path: assets/images/chinese_diandu_icon.png
  AssetGenImage get chineseDianduIcon =>
      const AssetGenImage('assets/images/chinese_diandu_icon.png');

  /// File path: assets/images/chinese_down_arrow_icon.png
  AssetGenImage get chineseDownArrowIcon =>
      const AssetGenImage('assets/images/chinese_down_arrow_icon.png');

  /// File path: assets/images/chinese_fold_up_arrow.png
  AssetGenImage get chineseFoldUpArrow =>
      const AssetGenImage('assets/images/chinese_fold_up_arrow.png');

  /// File path: assets/images/chinese_item_bg.png
  AssetGenImage get chineseItemBg =>
      const AssetGenImage('assets/images/chinese_item_bg.png');

  /// File path: assets/images/chinese_item_bg2.png
  AssetGenImage get chineseItemBg2 =>
      const AssetGenImage('assets/images/chinese_item_bg2.png');

  /// File path: assets/images/chinese_item_bg3.png
  AssetGenImage get chineseItemBg3 =>
      const AssetGenImage('assets/images/chinese_item_bg3.png');

  /// File path: assets/images/chinese_item_bg4.png
  AssetGenImage get chineseItemBg4 =>
      const AssetGenImage('assets/images/chinese_item_bg4.png');

  /// File path: assets/images/chinese_item_bg_bottom.png
  AssetGenImage get chineseItemBgBottom =>
      const AssetGenImage('assets/images/chinese_item_bg_bottom.png');

  /// File path: assets/images/chinese_item_bg_middle.png
  AssetGenImage get chineseItemBgMiddle =>
      const AssetGenImage('assets/images/chinese_item_bg_middle.png');

  /// File path: assets/images/chinese_item_bg_top.png
  AssetGenImage get chineseItemBgTop =>
      const AssetGenImage('assets/images/chinese_item_bg_top.png');

  /// File path: assets/images/chinese_lock_icon.png
  AssetGenImage get chineseLockIcon =>
      const AssetGenImage('assets/images/chinese_lock_icon.png');

  /// File path: assets/images/chinese_point_read_bg.png
  AssetGenImage get chinesePointReadBg =>
      const AssetGenImage('assets/images/chinese_point_read_bg.png');

  /// File path: assets/images/chinese_popup_bg_bottom.png
  AssetGenImage get chinesePopupBgBottom =>
      const AssetGenImage('assets/images/chinese_popup_bg_bottom.png');

  /// File path: assets/images/chinese_popup_bg_middle.png
  AssetGenImage get chinesePopupBgMiddle =>
      const AssetGenImage('assets/images/chinese_popup_bg_middle.png');

  /// File path: assets/images/chinese_popup_bg_top.png
  AssetGenImage get chinesePopupBgTop =>
      const AssetGenImage('assets/images/chinese_popup_bg_top.png');

  /// File path: assets/images/chinese_pr_add_ponit_bg.png
  AssetGenImage get chinesePrAddPonitBg =>
      const AssetGenImage('assets/images/chinese_pr_add_ponit_bg.png');

  /// File path: assets/images/chinese_pr_aloud_read_disable_icon.png
  AssetGenImage get chinesePrAloudReadDisableIcon => const AssetGenImage(
      'assets/images/chinese_pr_aloud_read_disable_icon.png');

  /// File path: assets/images/chinese_pr_aloud_read_icon.png
  AssetGenImage get chinesePrAloudReadIcon =>
      const AssetGenImage('assets/images/chinese_pr_aloud_read_icon.png');

  /// File path: assets/images/chinese_pr_audio_player_icon.png
  AssetGenImage get chinesePrAudioPlayerIcon =>
      const AssetGenImage('assets/images/chinese_pr_audio_player_icon.png');

  /// File path: assets/images/chinese_pr_botttom_bg.png
  AssetGenImage get chinesePrBotttomBg =>
      const AssetGenImage('assets/images/chinese_pr_botttom_bg.png');

  /// File path: assets/images/chinese_pr_btn_bg.png
  AssetGenImage get chinesePrBtnBg =>
      const AssetGenImage('assets/images/chinese_pr_btn_bg.png');

  /// File path: assets/images/chinese_pr_btn_bg2.png
  AssetGenImage get chinesePrBtnBg2 =>
      const AssetGenImage('assets/images/chinese_pr_btn_bg2.png');

  /// File path: assets/images/chinese_pr_close_icon.png
  AssetGenImage get chinesePrCloseIcon =>
      const AssetGenImage('assets/images/chinese_pr_close_icon.png');

  /// File path: assets/images/chinese_pr_close_icon2.png
  AssetGenImage get chinesePrCloseIcon2 =>
      const AssetGenImage('assets/images/chinese_pr_close_icon2.png');

  /// File path: assets/images/chinese_pr_close_icon3.png
  AssetGenImage get chinesePrCloseIcon3 =>
      const AssetGenImage('assets/images/chinese_pr_close_icon3.png');

  /// File path: assets/images/chinese_pr_detail_bg.png
  AssetGenImage get chinesePrDetailBg =>
      const AssetGenImage('assets/images/chinese_pr_detail_bg.png');

  /// File path: assets/images/chinese_pr_e_bao.png
  AssetGenImage get chinesePrEBao =>
      const AssetGenImage('assets/images/chinese_pr_e_bao.png');

  /// File path: assets/images/chinese_pr_e_bao_love.png
  AssetGenImage get chinesePrEBaoLove =>
      const AssetGenImage('assets/images/chinese_pr_e_bao_love.png');

  /// File path: assets/images/chinese_pr_earn_points_today.png
  AssetGenImage get chinesePrEarnPointsToday =>
      const AssetGenImage('assets/images/chinese_pr_earn_points_today.png');

  /// File path: assets/images/chinese_pr_full_text_recite_bg.png
  AssetGenImage get chinesePrFullTextReciteBg =>
      const AssetGenImage('assets/images/chinese_pr_full_text_recite_bg.png');

  /// File path: assets/images/chinese_pr_horn.png
  AssetGenImage get chinesePrHorn =>
      const AssetGenImage('assets/images/chinese_pr_horn.png');

  /// File path: assets/images/chinese_pr_how_earn_points.png
  AssetGenImage get chinesePrHowEarnPoints =>
      const AssetGenImage('assets/images/chinese_pr_how_earn_points.png');

  /// File path: assets/images/chinese_pr_how_earn_points_tips.png
  AssetGenImage get chinesePrHowEarnPointsTips =>
      const AssetGenImage('assets/images/chinese_pr_how_earn_points_tips.png');

  /// File path: assets/images/chinese_pr_icon.png
  AssetGenImage get chinesePrIcon =>
      const AssetGenImage('assets/images/chinese_pr_icon.png');

  /// File path: assets/images/chinese_pr_indicator_selected_bg.png
  AssetGenImage get chinesePrIndicatorSelectedBg =>
      const AssetGenImage('assets/images/chinese_pr_indicator_selected_bg.png');

  /// File path: assets/images/chinese_pr_indicator_unselected_bg.png
  AssetGenImage get chinesePrIndicatorUnselectedBg => const AssetGenImage(
      'assets/images/chinese_pr_indicator_unselected_bg.png');

  /// File path: assets/images/chinese_pr_integral_icon.png
  AssetGenImage get chinesePrIntegralIcon =>
      const AssetGenImage('assets/images/chinese_pr_integral_icon.png');

  /// File path: assets/images/chinese_pr_interpret_icon.png
  AssetGenImage get chinesePrInterpretIcon =>
      const AssetGenImage('assets/images/chinese_pr_interpret_icon.png');

  /// File path: assets/images/chinese_pr_introduce_video_top.png
  AssetGenImage get chinesePrIntroduceVideoTop =>
      const AssetGenImage('assets/images/chinese_pr_introduce_video_top.png');

  /// File path: assets/images/chinese_pr_left_btn_bg.png
  AssetGenImage get chinesePrLeftBtnBg =>
      const AssetGenImage('assets/images/chinese_pr_left_btn_bg.png');

  /// File path: assets/images/chinese_pr_magnifier_icon.png
  AssetGenImage get chinesePrMagnifierIcon =>
      const AssetGenImage('assets/images/chinese_pr_magnifier_icon.png');

  /// File path: assets/images/chinese_pr_mark_icon.png
  AssetGenImage get chinesePrMarkIcon =>
      const AssetGenImage('assets/images/chinese_pr_mark_icon.png');

  /// File path: assets/images/chinese_pr_microphone_icon.png
  AssetGenImage get chinesePrMicrophoneIcon =>
      const AssetGenImage('assets/images/chinese_pr_microphone_icon.png');

  /// File path: assets/images/chinese_pr_pause_icon.png
  AssetGenImage get chinesePrPauseIcon =>
      const AssetGenImage('assets/images/chinese_pr_pause_icon.png');

  /// File path: assets/images/chinese_pr_play_icon.png
  AssetGenImage get chinesePrPlayIcon =>
      const AssetGenImage('assets/images/chinese_pr_play_icon.png');

  /// File path: assets/images/chinese_pr_point_read_disable_icon.png
  AssetGenImage get chinesePrPointReadDisableIcon => const AssetGenImage(
      'assets/images/chinese_pr_point_read_disable_icon.png');

  /// File path: assets/images/chinese_pr_point_read_icon.png
  AssetGenImage get chinesePrPointReadIcon =>
      const AssetGenImage('assets/images/chinese_pr_point_read_icon.png');

  /// File path: assets/images/chinese_pr_practice_icon.png
  AssetGenImage get chinesePrPracticeIcon =>
      const AssetGenImage('assets/images/chinese_pr_practice_icon.png');

  /// File path: assets/images/chinese_pr_preview_author.png
  AssetGenImage get chinesePrPreviewAuthor =>
      const AssetGenImage('assets/images/chinese_pr_preview_author.png');

  /// File path: assets/images/chinese_pr_preview_difficulty_tag.png
  AssetGenImage get chinesePrPreviewDifficultyTag => const AssetGenImage(
      'assets/images/chinese_pr_preview_difficulty_tag.png');

  /// File path: assets/images/chinese_pr_preview_disable_icon.png
  AssetGenImage get chinesePrPreviewDisableIcon =>
      const AssetGenImage('assets/images/chinese_pr_preview_disable_icon.png');

  /// File path: assets/images/chinese_pr_preview_icon.png
  AssetGenImage get chinesePrPreviewIcon =>
      const AssetGenImage('assets/images/chinese_pr_preview_icon.png');

  /// File path: assets/images/chinese_pr_preview_link_tag.png
  AssetGenImage get chinesePrPreviewLinkTag =>
      const AssetGenImage('assets/images/chinese_pr_preview_link_tag.png');

  /// File path: assets/images/chinese_pr_preview_method_tag.png
  AssetGenImage get chinesePrPreviewMethodTag =>
      const AssetGenImage('assets/images/chinese_pr_preview_method_tag.png');

  /// File path: assets/images/chinese_pr_preview_mm_tag.png
  AssetGenImage get chinesePrPreviewMmTag =>
      const AssetGenImage('assets/images/chinese_pr_preview_mm_tag.png');

  /// File path: assets/images/chinese_pr_preview_tag_bg.png
  AssetGenImage get chinesePrPreviewTagBg =>
      const AssetGenImage('assets/images/chinese_pr_preview_tag_bg.png');

  /// File path: assets/images/chinese_pr_preview_theme_tag.png
  AssetGenImage get chinesePrPreviewThemeTag =>
      const AssetGenImage('assets/images/chinese_pr_preview_theme_tag.png');

  /// File path: assets/images/chinese_pr_read_aloud_12_bg.png
  AssetGenImage get chinesePrReadAloud12Bg =>
      const AssetGenImage('assets/images/chinese_pr_read_aloud_12_bg.png');

  /// File path: assets/images/chinese_pr_read_aloud_34_bg.png
  AssetGenImage get chinesePrReadAloud34Bg =>
      const AssetGenImage('assets/images/chinese_pr_read_aloud_34_bg.png');

  /// File path: assets/images/chinese_pr_read_aloud_56_bg.png
  AssetGenImage get chinesePrReadAloud56Bg =>
      const AssetGenImage('assets/images/chinese_pr_read_aloud_56_bg.png');

  /// File path: assets/images/chinese_pr_read_countdown.svga
  String get chinesePrReadCountdown =>
      'packages/yyb_text_reading_points/assets/images/chinese_pr_read_countdown.svga';

  /// File path: assets/images/chinese_pr_recite_12_bg.png
  AssetGenImage get chinesePrRecite12Bg =>
      const AssetGenImage('assets/images/chinese_pr_recite_12_bg.png');

  /// File path: assets/images/chinese_pr_recite_34_bg.png
  AssetGenImage get chinesePrRecite34Bg =>
      const AssetGenImage('assets/images/chinese_pr_recite_34_bg.png');

  /// File path: assets/images/chinese_pr_recite_56_bg.png
  AssetGenImage get chinesePrRecite56Bg =>
      const AssetGenImage('assets/images/chinese_pr_recite_56_bg.png');

  /// File path: assets/images/chinese_pr_recite_countdown.svga
  String get chinesePrReciteCountdown =>
      'packages/yyb_text_reading_points/assets/images/chinese_pr_recite_countdown.svga';

  /// File path: assets/images/chinese_pr_recite_disable_icon.png
  AssetGenImage get chinesePrReciteDisableIcon =>
      const AssetGenImage('assets/images/chinese_pr_recite_disable_icon.png');

  /// File path: assets/images/chinese_pr_recite_icon.png
  AssetGenImage get chinesePrReciteIcon =>
      const AssetGenImage('assets/images/chinese_pr_recite_icon.png');

  /// File path: assets/images/chinese_pr_recite_selected_bg.png
  AssetGenImage get chinesePrReciteSelectedBg =>
      const AssetGenImage('assets/images/chinese_pr_recite_selected_bg.png');

  /// File path: assets/images/chinese_pr_review_disable_icon.png
  AssetGenImage get chinesePrReviewDisableIcon =>
      const AssetGenImage('assets/images/chinese_pr_review_disable_icon.png');

  /// File path: assets/images/chinese_pr_review_icon.png
  AssetGenImage get chinesePrReviewIcon =>
      const AssetGenImage('assets/images/chinese_pr_review_icon.png');

  /// File path: assets/images/chinese_pr_right_btn_bg.png
  AssetGenImage get chinesePrRightBtnBg =>
      const AssetGenImage('assets/images/chinese_pr_right_btn_bg.png');

  /// File path: assets/images/chinese_pr_setting_icon.png
  AssetGenImage get chinesePrSettingIcon =>
      const AssetGenImage('assets/images/chinese_pr_setting_icon.png');

  /// File path: assets/images/chinese_pr_share_icon.png
  AssetGenImage get chinesePrShareIcon =>
      const AssetGenImage('assets/images/chinese_pr_share_icon.png');

  /// File path: assets/images/chinese_pr_share_icon2.png
  AssetGenImage get chinesePrShareIcon2 =>
      const AssetGenImage('assets/images/chinese_pr_share_icon2.png');

  /// File path: assets/images/chinese_pr_unfamiliar_words_icon.png
  AssetGenImage get chinesePrUnfamiliarWordsIcon =>
      const AssetGenImage('assets/images/chinese_pr_unfamiliar_words_icon.png');

  /// File path: assets/images/chinese_right_arrow_icon.png
  AssetGenImage get chineseRightArrowIcon =>
      const AssetGenImage('assets/images/chinese_right_arrow_icon.png');

  /// File path: assets/images/chinese_tab_selected_bg.png
  AssetGenImage get chineseTabSelectedBg =>
      const AssetGenImage('assets/images/chinese_tab_selected_bg.png');

  /// File path: assets/images/chinese_tab_unselected_bg.png
  AssetGenImage get chineseTabUnselectedBg =>
      const AssetGenImage('assets/images/chinese_tab_unselected_bg.png');

  /// File path: assets/images/chinese_up_arrow_icon.png
  AssetGenImage get chineseUpArrowIcon =>
      const AssetGenImage('assets/images/chinese_up_arrow_icon.png');

  /// File path: assets/images/ding.wav
  String get ding => 'packages/yyb_text_reading_points/assets/images/ding.wav';

  /// File path: assets/images/ew_rank_1.png
  AssetGenImage get ewRank1 =>
      const AssetGenImage('assets/images/ew_rank_1.png');

  /// File path: assets/images/ew_rank_2.png
  AssetGenImage get ewRank2 =>
      const AssetGenImage('assets/images/ew_rank_2.png');

  /// File path: assets/images/ew_rank_3.png
  AssetGenImage get ewRank3 =>
      const AssetGenImage('assets/images/ew_rank_3.png');

  /// File path: assets/images/question_spoke_play1.png
  AssetGenImage get questionSpokePlay1 =>
      const AssetGenImage('assets/images/question_spoke_play1.png');

  /// File path: assets/images/recit_anim_icon1.png
  AssetGenImage get recitAnimIcon1 =>
      const AssetGenImage('assets/images/recit_anim_icon1.png');

  /// File path: assets/images/recit_anim_icon2.png
  AssetGenImage get recitAnimIcon2 =>
      const AssetGenImage('assets/images/recit_anim_icon2.png');

  /// File path: assets/images/recite_anim_icon3.png
  AssetGenImage get reciteAnimIcon3 =>
      const AssetGenImage('assets/images/recite_anim_icon3.png');

  /// File path: assets/images/recite_anim_icon4.png
  AssetGenImage get reciteAnimIcon4 =>
      const AssetGenImage('assets/images/recite_anim_icon4.png');

  /// File path: assets/images/suspension_play.png
  AssetGenImage get suspensionPlay =>
      const AssetGenImage('assets/images/suspension_play.png');

  /// File path: assets/images/suspension_stop.png
  AssetGenImage get suspensionStop =>
      const AssetGenImage('assets/images/suspension_stop.png');

  /// Directory path: assets/images
  String get path => 'assets/images';

  /// List of all assets
  List<dynamic> get values => [
        bottomBtnNextIcon,
        bottomBtnOriginalSound,
        bottomBtnOriginalSoundPlay1,
        bottomBtnOriginalSoundPlay2,
        bottomBtnOriginalSoundPlay3,
        bottomBtnReplayIcon,
        bottomBtnReplayIcon1,
        bottomBtnReplayIcon2,
        bottomBtnResetIcon,
        bottomBtnSubmitIcon,
        bottomBtnUnNextIcon,
        bottomBtnUnReplayIcon,
        bottomBtnUnResetIcon,
        bottomBtnUnSubmitIcon,
        chineseArrowIcon,
        chineseBackIcon,
        chineseBoxNor,
        chineseBoxSel,
        chineseBoxUnSel,
        chineseButtomBg,
        chineseDescribeContent,
        chineseDescribeTop,
        chineseDianduIcon,
        chineseDownArrowIcon,
        chineseFoldUpArrow,
        chineseItemBg,
        chineseItemBg2,
        chineseItemBg3,
        chineseItemBg4,
        chineseItemBgBottom,
        chineseItemBgMiddle,
        chineseItemBgTop,
        chineseLockIcon,
        chinesePointReadBg,
        chinesePopupBgBottom,
        chinesePopupBgMiddle,
        chinesePopupBgTop,
        chinesePrAddPonitBg,
        chinesePrAloudReadDisableIcon,
        chinesePrAloudReadIcon,
        chinesePrAudioPlayerIcon,
        chinesePrBotttomBg,
        chinesePrBtnBg,
        chinesePrBtnBg2,
        chinesePrCloseIcon,
        chinesePrCloseIcon2,
        chinesePrCloseIcon3,
        chinesePrDetailBg,
        chinesePrEBao,
        chinesePrEBaoLove,
        chinesePrEarnPointsToday,
        chinesePrFullTextReciteBg,
        chinesePrHorn,
        chinesePrHowEarnPoints,
        chinesePrHowEarnPointsTips,
        chinesePrIcon,
        chinesePrIndicatorSelectedBg,
        chinesePrIndicatorUnselectedBg,
        chinesePrIntegralIcon,
        chinesePrInterpretIcon,
        chinesePrIntroduceVideoTop,
        chinesePrLeftBtnBg,
        chinesePrMagnifierIcon,
        chinesePrMarkIcon,
        chinesePrMicrophoneIcon,
        chinesePrPauseIcon,
        chinesePrPlayIcon,
        chinesePrPointReadDisableIcon,
        chinesePrPointReadIcon,
        chinesePrPracticeIcon,
        chinesePrPreviewAuthor,
        chinesePrPreviewDifficultyTag,
        chinesePrPreviewDisableIcon,
        chinesePrPreviewIcon,
        chinesePrPreviewLinkTag,
        chinesePrPreviewMethodTag,
        chinesePrPreviewMmTag,
        chinesePrPreviewTagBg,
        chinesePrPreviewThemeTag,
        chinesePrReadAloud12Bg,
        chinesePrReadAloud34Bg,
        chinesePrReadAloud56Bg,
        chinesePrReadCountdown,
        chinesePrRecite12Bg,
        chinesePrRecite34Bg,
        chinesePrRecite56Bg,
        chinesePrReciteCountdown,
        chinesePrReciteDisableIcon,
        chinesePrReciteIcon,
        chinesePrReciteSelectedBg,
        chinesePrReviewDisableIcon,
        chinesePrReviewIcon,
        chinesePrRightBtnBg,
        chinesePrSettingIcon,
        chinesePrShareIcon,
        chinesePrShareIcon2,
        chinesePrUnfamiliarWordsIcon,
        chineseRightArrowIcon,
        chineseTabSelectedBg,
        chineseTabUnselectedBg,
        chineseUpArrowIcon,
        ding,
        ewRank1,
        ewRank2,
        ewRank3,
        questionSpokePlay1,
        recitAnimIcon1,
        recitAnimIcon2,
        reciteAnimIcon3,
        reciteAnimIcon4,
        suspensionPlay,
        suspensionStop
      ];
}

class Assets {
  Assets._();

  static const String package = 'yyb_text_reading_points';

  static const $AssetsImagesGen images = $AssetsImagesGen();
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  });

  final String _assetName;

  static const String package = 'yyb_text_reading_points';

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
    FilterQuality filterQuality = FilterQuality.low,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => 'packages/yyb_text_reading_points/$_assetName';
}
