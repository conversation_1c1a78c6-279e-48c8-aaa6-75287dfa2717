import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lib_base/config/route_utils.dart';
import 'package:lib_base/config/theme_config.dart';
import 'package:lib_base/generated/assets.gen.dart';
import 'package:lib_base/log/log.dart';
import 'package:lib_base/pages/video_page/english_video_play/widgets/exit_video_learn_dialog.dart';
import 'package:lib_base/pages/video_page/english_video_play/widgets/trial_over_dialog.dart';
import 'package:lib_base/pages/video_page/english_video_play/widgets/vip_request_dialog.dart';
import 'package:lib_base/providers/user/user_info_provider.dart';
import 'package:lib_base/resource/plugins/window_private/window_private_plugin.dart';
import 'package:lib_base/utils/business/image_util.dart';
import 'package:lib_base/utils/ui_util.dart';
import 'package:lib_base/widgets/image/net_cache_image.dart';
import 'package:lib_base/widgets/video/controller/m_video_player_controller.dart';
import 'package:lib_base/widgets/video/full_screen_page.dart';
import 'package:lib_base/widgets/video/normal_video_player/widgets/m_normal_video_progress_indicator.dart';
import 'package:lib_base/widgets/video/short_video_player/widgets/video_play_button.dart';
import 'package:video_player/video_player.dart';

class EnglishVideoPlayPageParam {
  /**
   * 教材Id
   */
  final String bookId;
  /**
   * 模块Id
   */
  final String moduleId;
  /**
   * 推广组
   */
  final String promoteGroup;
 
  /**
   * 视频名称
   */
  final String videoName;
  /**
   * 视频Url
   */
  final String videoUrl;
  /**
   * 试看时长(单位:秒)
   */
  final num trialTime;
  /**
   * 视频总时长(单位:秒)
   */
  final num totalTime;
  /**
   * 是否为免费
   */
  final bool isFree;

  /**
   * 禁止录屏
   */
  final bool isProhibitScreencap;

  final String? videoImageUrl;
  final bool isFile;
  final bool autoPlay;

  EnglishVideoPlayPageParam(
      {required this.bookId,
      required this.moduleId,
      required this.promoteGroup,
      required this.videoName,
      required this.videoUrl,
      required this.trialTime,
      required this.totalTime,
      required this.isFree,
      required this.isProhibitScreencap,
      this.autoPlay = true,
      this.isFile = false,
      this.videoImageUrl});
}

class EnglishVideoPlayPage extends ConsumerStatefulWidget {
  final EnglishVideoPlayPageParam param;
  const EnglishVideoPlayPage({super.key, required this.param});

  @override
  ConsumerState<EnglishVideoPlayPage> createState() =>
      _EnglishVideoPlayPageState();
}

class _EnglishVideoPlayPageState extends ConsumerState<EnglishVideoPlayPage> {
  late MVideoPlayerController _controller;

  ValueNotifier<bool> _hideMenusNotifier = ValueNotifier(true);

  ValueNotifier<bool> _isLockedNotifier = ValueNotifier(false);

  ValueNotifier<bool> _hideBuyVipTipsNotifier = ValueNotifier(false);

  Timer? _timer;

  //鸿蒙的播放器有bug， 初始化好以后， 播放器依然是白屏, 所以用这个控制一下
  bool _isPlayed = false;

// 是否是试看
  bool _isInTrial = false;

  @override
  void initState() {
    super.initState();
    var userInfo = ref.read(userInfoNotifierProvider);
    //是否是试看
    _isInTrial = !(widget.param.isFree || userInfo.isPtMember);
    if (widget.param.isProhibitScreencap) {
      //禁止录屏
      WindowPrivatePlugin.setWindowPrivate(true);
    }
    _controller = MVideoPlayerController();
    _initController();
  }

  Future _doBack() async {
    _controller.videoController?.pause();
    //弹出返回弹框
    showSmartDialog(ExitVideoLearnDialog(
      doContinue: () {
        dismissDialog();
        _controller.videoController?.play();
      },
      doExit: () {
        dismissDialog();
        back(_controller.value);
      },
    ));
  }

  Future _initController() async {
    await _controller.initController(
        videoUrl: ImageUtil.getImageUrl(widget.param.videoUrl),
        isFile: widget.param.isFile);
    _controller.initialize().then((value) async {
      Logger.info("=======  controller initialized");

      if (_isInTrial) {
        if (widget.param.trialTime > 0) {
          if (widget.param.autoPlay) {
            showToast("此视频为付费会员视频，可试看${widget.param.trialTime}秒钟");
            _controller.play();
          }
        } else {
          // 弹出vip对话框
          _showVipRequestDialog();
        }
      } else {
        if (widget.param.autoPlay) {
          _controller.play();
        }
      }

      setState(() {});
    });

    _controller.videoController?.addListener(() async {
      bool isPlaying = _controller.videoController!.value.isPlaying;
      if (isPlaying && !_isPlayed) {
        //鸿蒙的播放器有bug， 初始化好以后， 播放器依然是白屏, 所以用这个控制一下
        _isPlayed = true;
        setState(() {});
      }
      if (await _isOutOfTrialTime()) {
        //已经过了试看, 停止播放
        _controller.videoController?.pause();
        // 弹出vip对话框
        _showTripOverDialog();
      }
    });
  }

//试看完成后的弹框
  void _showTripOverDialog() {
    showSmartDialog(TrialOverDialog(
      toBuyVip: () {
        dismissDialog();
        _toVip();
      },
      onExit: () {
        dismissDialog();
        back();
      },
    ));
  }

  void _showVipRequestDialog() {
    //  弹出vip对话框
    showSmartDialog(VipRequestDialog(
      onConfirm: () {
        dismissDialog();
        _toVip();
      },
    ));
  }

  void _toVip() {
    back();
    toInteractWebviewModulePage(moduleId: "20", showBack: true, othermap: {
      "moduleConfigId": widget.param.moduleId,
      "type": "1",
      "channel": "1",
      "promote_group": widget.param.promoteGroup
    });
  }

  Future<bool> _isOutOfTrialTime() async {
    if (!_isInTrial) {
      return false;
    } else {
      if (widget.param.trialTime > 0) {
        //监控进度
        Duration? currentPosition = await _controller.videoController!.position;

        if (currentPosition != null &&
            _isInTrial &&
            widget.param.trialTime > 0) {
          //不免费，而且试看时间大于0
          Duration trialTimeDuration =
              Duration(seconds: widget.param.trialTime.toInt());
          if (trialTimeDuration.compareTo(currentPosition) > 0) {
            return false;
          }
        }
      }
      return true;
    }
  }

  @override
  void dispose() {
    super.dispose();
    if (widget.param.isProhibitScreencap) {
      //禁止录屏
      WindowPrivatePlugin.setWindowPrivate(false);
    }
    _controller.dispose();
    _timer?.cancel();
  }

  void _autoSwitchProgressVisible() {
    _hideMenusNotifier.value = !_hideMenusNotifier.value;
    if (_hideMenusNotifier.value) {
      //如果是隐藏
      _timer?.cancel();
    } else {
      //如果是显示
      _timer = Timer.periodic(Duration(seconds: 5), (timer) {
        _hideMenusNotifier.value = true;
        timer.cancel();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return FullScreenPage(
      child: PopScope(
        canPop: false,
        onPopInvoked: (didPop) async{
          if (!didPop) {
          await _doBack();
        }
        },
        
        child: SizedBox(
          width: 1.sw,
          height: 1.sh,
          child: Stack(
            alignment: Alignment.center,
            children: [
              _playerWidget(),
              //播放按钮
              _playButton(),

              _progressWidget(),
              _headerWidget(),
              _lockWidget(),
              //试看，就弹vip
              if (_isInTrial) _buyVipTipsWidget(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _playButton() {
    return _controller.videoController != null
        ? VideoPlayButton(
            _controller.videoController!,
            doPlay: (c) async {
              if (await _isOutOfTrialTime()) {
                //过了试看
                // 弹出vip对话框
                _showVipRequestDialog();
              } else {
                //播放
                c.play();
              }
            },
          )
        : const SizedBox.shrink();
  }

  Widget _playerWidget() {
    return Container(
      alignment: Alignment.center,
      color: ThemeConfig.currentTheme.colorBlack,
      child: ((_controller.value?.isInitialized ?? false) && _isPlayed)
          ? AspectRatio(
              aspectRatio: _controller.value!.aspectRatio,
              child: InkWell(
                  onTap: () {
                    _autoSwitchProgressVisible();
                  },
                  child: new VideoPlayer(_controller.videoController!)))
          : widget.param.videoImageUrl?.isNotEmpty ?? false
              ? BaseNetCacheImage(
                  imageUrl: ImageUtil.getImageUrl(widget.param.videoImageUrl!),
                  loadingWidget: SizedBox.shrink(),
                )
              : Container(
                  color: Colors.yellow,
                ),
    );
  }

  Widget _progressWidget() {
    return Positioned(
      bottom: 15.h,
      child: ValueListenableBuilder(
        valueListenable: _hideMenusNotifier,
        builder: (_, value, child) {
          return Offstage(
            offstage: !(_controller.value?.isInitialized ?? false) || value,
            child: child,
          );
        },
        child: (_controller.value?.isInitialized ?? false)
            ? ValueListenableBuilder(
                valueListenable: _isLockedNotifier,
                builder: (_, isLocked, c) {
                  return isLocked
                      ? const SizedBox.shrink()
                      : Container(
                          alignment: Alignment.center,
                          padding: EdgeInsets.symmetric(horizontal: 10.w),
                          width: 1.sw,
                          height: 55.h,
                          child: MNormalVideoProgressIndicator(
                            _controller.videoController!,
                            showFullScreenTap: false,
                            showSpeedControl: true,
                            allowScrubbing: true,
                            padding: EdgeInsets.symmetric(vertical: 15.h),
                            seekWidget: SeekWidgetModel(
                                seekWidget: BaseAssets
                                    .images.videoPlayerSeekIcon
                                    .image(width: 30.r, height: 30.r),
                                width: 30.r),
                            doPlay: (c) async {
                              if (await _isOutOfTrialTime()) {
                                //过了试看
                                // 弹出vip对话框
                                _showVipRequestDialog();
                              } else {
                                //播放
                                c.play();
                              }
                            },
                          ),
                        );
                })
            : const SizedBox.shrink(),
      ),
    );
  }

  Widget _headerWidget() {
    return Positioned(
      left: 10,
      top: MediaQuery.of(context).padding.top,
      child: ValueListenableBuilder(
        valueListenable: _hideMenusNotifier,
        builder: (_, value, child) {
          return Offstage(
            offstage: !(_controller.value?.isInitialized ?? false) || value,
            child: child,
          );
        },
        child: InkWell(
          onTap: () {
            _doBack();
          },
          child: Container(
            padding: EdgeInsets.all(10.r),
            child: Row(
              children: [
                Icon(
                  Icons.arrow_back_ios,
                  color: ThemeConfig.currentTheme.colorWhite,
                  size: 22.sp,
                ),
                Text(
                  widget.param.videoName,
                  style: ThemeConfig.currentTheme.text17WhiteM,
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buyVipTipsWidget() {
    return Positioned(
      right: 30,
      top: MediaQuery.of(context).padding.top + 20,
      child: ValueListenableBuilder(
        valueListenable: _hideMenusNotifier,
        builder: (_, value, child) {
          return Offstage(
            offstage: !(_controller.value?.isInitialized ?? false) || value,
            child: child,
          );
        },
        child: ValueListenableBuilder(
            valueListenable: _hideBuyVipTipsNotifier,
            builder: (_, hideBuyVipTips, child) {
              return hideBuyVipTips
                  ? const SizedBox.shrink()
                  : Container(
                      padding: EdgeInsets.all(10.r),
                      decoration: BoxDecoration(
                        color: ThemeConfig.currentTheme.colorBlack,
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Row(
                        children: [
                          InkWell(
                            onTap: () {
                              _hideBuyVipTipsNotifier.value = true;
                            },
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Icon(
                                Icons.close,
                                color: ThemeConfig.currentTheme.colorWhite,
                                size: 22.sp,
                              ),
                            ),
                          ),
                          Text(
                            "您正在试看，购买VIP会员解锁全部课程",
                            style: ThemeConfig.currentTheme.text15White,
                          ),
                          InkWell(
                            onTap: () {
                              _toVip();
                            },
                            child: Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    "购买会员",
                                    style:
                                        ThemeConfig.currentTheme.text15Yellow,
                                  ),
                                  Icon(
                                    Icons.arrow_right,
                                    size: 22.sp,
                                    color: ThemeConfig.currentTheme.colorYellow,
                                  )
                                ],
                              ),
                            ),
                          )
                        ],
                      ),
                    );
            }),
      ),
    );
  }

  Widget _lockWidget() {
    return Positioned(
      left: MediaQuery.of(context).padding.left + 30,
      top: 0.5.sh - 18,
      child: ValueListenableBuilder(
        valueListenable: _hideMenusNotifier,
        builder: (_, value, child) {
          return Offstage(
            offstage: !(_controller.value?.isInitialized ?? false) || value,
            child: child,
          );
        },
        child: InkWell(
          onTap: () {
            _isLockedNotifier.value = !_isLockedNotifier.value;
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: ValueListenableBuilder(
                valueListenable: _isLockedNotifier,
                builder: (_, isLocked, c) {
                  return Container(
                    padding: EdgeInsets.all(10.r),
                    child: Row(
                      children: [
                        Icon(
                          isLocked ? Icons.lock : Icons.lock_open_outlined,
                          color: ThemeConfig.currentTheme.colorWhite,
                          size: 32.sp,
                        ),
                      ],
                    ),
                  );
                }),
          ),
        ),
      ),
    );
  }
}
