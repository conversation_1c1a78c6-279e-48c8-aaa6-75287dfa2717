// @keepTs
// @ts-nocheck
import AbilityAware from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/ability/AbilityAware';
import { AbilityPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/ability/AbilityPluginBinding';
import { FlutterPlugin, FlutterPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import common from '@ohos.app.ability.common';
import Ability from '@ohos.app.ability.Ability';
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import ImagePickerDelegate from './ImagePickerDelegate';
import { GeneralOptions, ImagePickerApi, ImageSelectionOptions, SourceSpecification, Result, MediaSelectionOptions, CacheRetrievalResult, VideoSelectionOptions } from './Messages';
import UIAbility from '@ohos.app.ability.UIAbility';
import ArrayList from '@ohos.util.ArrayList';
export default class ImagePickerPlugin implements FlutterPlugin, AbilityAware {
    private static TAG;
    private pluginBinding;
    private state;
    getUniqueClassName(): string;
    onAttachedToEngine(n6: FlutterPluginBinding): void;
    onDetachedFromEngine(m6: FlutterPluginBinding): void;
    onAttachedToAbility(l6: AbilityPluginBinding): void;
    onDetachedFromAbility(): void;
    setup(h6: BinaryMessenger, i6: common.Context, j6: UIAbility, k6: AbilityPluginBinding): void;
    constructor(f6?: ImagePickerDelegate, g6?: UIAbility);
    pickImages(b6: SourceSpecification, c6: ImageSelectionOptions, d6: GeneralOptions, e6: Result<ArrayList<string>>): void;
    pickVideos(x5: SourceSpecification, y5: VideoSelectionOptions, z5: GeneralOptions, a6: Result<ArrayList<string>>): void;
    pickMedia(u5: MediaSelectionOptions, v5: GeneralOptions, w5: Result<ArrayList<string>>): void;
    retrieveLostResults(): Promise<CacheRetrievalResult | null>;
    getAbilityState(): AbilityState | null;
    static constructorDelegate(t5: UIAbility): ImagePickerDelegate;
    getImagePickerDelegate(): ImagePickerDelegate | null;
    setCameraDevice(r5: ImagePickerDelegate, s5: SourceSpecification): void;
}
declare class AbilityState {
    private ability;
    private context;
    private abilityBinding;
    private messenger;
    private delegate;
    private abilityLifecycleCallback;
    constructor(l5: UIAbility, m5?: ImagePickerDelegate | null, n5?: BinaryMessenger, o5?: ImagePickerApi, p5?: AbilityPluginBinding, q5?: common.Context);
    release(): void;
    getAbility(): Ability | null;
    getDelegate(): ImagePickerDelegate | null;
}
export {};
