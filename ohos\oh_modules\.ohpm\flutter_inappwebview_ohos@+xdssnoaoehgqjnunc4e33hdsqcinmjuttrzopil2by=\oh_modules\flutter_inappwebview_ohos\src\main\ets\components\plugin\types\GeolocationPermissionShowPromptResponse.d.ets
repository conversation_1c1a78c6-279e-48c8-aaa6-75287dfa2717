// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
export default class GeolocationPermissionShowPromptResponse {
    private origin;
    allow: boolean;
    retain: boolean;
    constructor(t21: string, u21: boolean, v21: boolean);
    static fromMap(s21: Map<string, Any>): GeolocationPermissionShowPromptResponse | null;
    getOrigin(): string;
    setOrigin(r21: string): void;
    isAllow(): boolean;
    setAllow(q21: boolean): void;
    isRetain(): boolean;
    setRetain(p21: boolean): void;
    toString(): string;
}
