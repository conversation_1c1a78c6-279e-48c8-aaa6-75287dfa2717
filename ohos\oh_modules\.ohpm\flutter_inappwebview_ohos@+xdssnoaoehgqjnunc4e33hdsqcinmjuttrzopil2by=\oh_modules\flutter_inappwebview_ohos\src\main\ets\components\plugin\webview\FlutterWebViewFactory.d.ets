// @keepTs
// @ts-nocheck
import { PlatformView, PlatformViewFactory } from '@ohos/flutter_ohos';
import InAppWebViewFlutterPlugin from '../InAppWebViewFlutterPlugin';
import { Any } from '@ohos/flutter_ohos';
export default class FlutterWebViewFactory extends PlatformViewFactory {
    static VIEW_TYPE_ID: string;
    private plugin;
    constructor(f1: InAppWebViewFlutterPlugin);
    create(c1: Context, d1: number, e1: Any): PlatformView;
}
