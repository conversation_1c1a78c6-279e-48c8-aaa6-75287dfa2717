// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
export default class JsPromptResponse {
    private message;
    private defaultValue;
    private confirmButtonTitle;
    private cancelButtonTitle;
    private handledByClient;
    private value;
    private action;
    constructor(o20: string, p20: string, q20: string, r20: string, s20: boolean, t20: string | null, u20: number | null);
    static fromMap(n20: Map<string, Any>): JsPromptResponse | null;
    getMessage(): string;
    setMessage(m20: string): void;
    getDefaultValue(): string;
    setDefaultValue(l20: string): void;
    getConfirmButtonTitle(): string;
    setConfirmButtonTitle(k20: string): void;
    getCancelButtonTitle(): string;
    setCancelButtonTitle(j20: string): void;
    isHandledByClient(): boolean;
    setHandledByClient(i20: boolean): void;
    getValue(): string | null;
    setValue(h20: string | null): void;
    getAction(): number | null;
    setAction(g20: number | null): void;
    equals(f20: Any): boolean;
    hashCode(): number;
    toString(): string;
}
