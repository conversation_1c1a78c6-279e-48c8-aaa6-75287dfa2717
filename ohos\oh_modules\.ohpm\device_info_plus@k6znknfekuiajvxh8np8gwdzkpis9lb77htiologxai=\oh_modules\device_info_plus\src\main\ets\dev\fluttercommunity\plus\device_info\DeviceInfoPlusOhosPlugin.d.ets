// @keepTs
// @ts-nocheck
import { FlutterPlugin, FlutterPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
/** DeviceInfoPlusOhosPlugin  */
export default class DeviceInfoPlusOhosPlugin implements FlutterPlugin {
    private channel;
    getUniqueClassName(): string;
    onAttachedToEngine(d: FlutterPluginBinding): void;
    onDetachedFromEngine(c: FlutterPluginBinding): void;
}
