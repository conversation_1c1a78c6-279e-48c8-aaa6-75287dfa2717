// @keepTs
// @ts-nocheck
import uri from '@ohos.uri';
export interface PathHandler {
    handle(path: string): WebResourceResponse;
}
export declare class AssetsPathHandler implements PathHandler {
    private context;
    constructor(d30: Context);
    handle(c30: string): WebResourceResponse;
}
export declare class ResourcesPathHandler implements PathHandler {
    private context;
    constructor(b30: Context);
    handle(a30: string): WebResourceResponse;
}
export declare class InternalStoragePathHandler implements PathHandler {
    private static FORBIDDEN_DATA_DIRS;
    private context;
    private directory;
    constructor(y29: Context, z29: string);
    handle(x29: string): WebResourceResponse;
}
export declare class PathMatcher {
    static HTTP_SCHEME: string;
    static HTTPS_SCHEME: string;
    mHttpEnabled: boolean;
    mAuthority: string | null;
    mPath: string | null;
    mHandler: PathHandler | null;
    constructor(t29: string, u29: string, v29: boolean, w29: PathHandler);
    match(s29: uri.URI): PathHandler | null;
    getSuffixPath(r29: string): string;
}
export default class WebViewAssetLoader {
    static DEFAULT_DOMAIN: string;
    private mMatchers;
    constructor(q29: Array<PathMatcher>);
    shouldInterceptRequest(p29: uri.URI): WebResourceResponse;
}
export declare class WebViewAssetLoaderBuilder {
    private mHttpAllowed;
    private mDomain;
    private mHandlerList;
    setDomain(o29: string): WebViewAssetLoaderBuilder;
    setHttpAllowed(n29: boolean): WebViewAssetLoaderBuilder;
    addPathHandler(l29: string, m29: PathHandler): this;
    build(): WebViewAssetLoader;
}
