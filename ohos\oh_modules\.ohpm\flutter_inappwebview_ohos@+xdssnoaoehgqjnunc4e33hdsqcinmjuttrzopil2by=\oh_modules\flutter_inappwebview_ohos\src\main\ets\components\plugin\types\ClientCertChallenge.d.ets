// @keepTs
// @ts-nocheck
import URLAuthenticationChallenge from './URLAuthenticationChallenge';
import URLProtectionSpace from './URLProtectionSpace';
export default class ClientCertChallenge extends URLAuthenticationChallenge {
    private principals;
    private keyTypes;
    constructor(f25: URLProtectionSpace, g25: Array<string> | null, h25: Array<string> | null);
    toMap(): Promise<Map<string, any>>;
    getPrincipals(): Array<string> | null;
    setPrincipals(e25: Array<string> | null): void;
    getKeyTypes(): Array<string> | null;
    setKeyTypes(d25: Array<string> | null): void;
}
