// @keepTs
// @ts-nocheck
export default class ResolutionExt {
    private id;
    private label;
    private verticalDpi;
    private horizontalDpi;
    constructor(o16: string, p16: string, q16: number, r16: number);
    static fromMap(n16: Map<String, Object>): ResolutionExt | null;
    toMap(): Map<String, Object>;
    getId(): string;
    setId(m16: string): void;
    getLabel(): string;
    setLabel(l16: string): void;
    getVerticalDpi(): number;
    setVerticalDpi(k16: number): void;
    getHorizontalDpi(): number;
    setHorizontalDpi(j16: number): void;
    equals(i16: Object): boolean;
    hashCode(): number;
    toString(): string;
}
