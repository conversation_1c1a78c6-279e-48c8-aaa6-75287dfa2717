import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lib_base/utils/ui_util.dart';
import 'package:svgaplayer_flutter/svgaplayer_flutter.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:yyb_text_reading_points/src/generated/assets.gen.dart';

class CountDownView extends ConsumerStatefulWidget {
  final VoidCallback callBack;
  const CountDownView({
    super.key,
    required this.callBack,
  });

  @override
  ConsumerState<CountDownView> createState() => _CountDownViewState();
}

class _CountDownViewState extends ConsumerState<CountDownView>
    with SingleTickerProviderStateMixin {
  late SVGAAnimationController _animationController;

  bool _isPlay = false;
  FlutterSoundPlayer _player = FlutterSoundPlayer();
  bool _mPlayerIsInited = false;

  @override
  void initState() {
    super.initState();
    _animationController = SVGAAnimationController(vsync: this);
    loadAnimation();
  }

  void loadAnimation() async {
    final videoItem = await SVGAParser.shared
        .decodeFromAssets("${Assets.images.chinesePrReciteCountdown}");
    _animationController.videoItem = videoItem;
    _animationController.forward().whenComplete(() {
      _animationController.videoItem = null;
      _startAudio(Assets.images.ding);
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _player.stopPlayer();
    _player.closePlayer();
    super.dispose();
  }

  Future _initPlayer() async {
    if (!_mPlayerIsInited) {
      await _player.openPlayer();
      _mPlayerIsInited = true;
    }
  }

  Future<void> _startAudio(String audiourl) async {
    await _initPlayer();
    if (!_isPlay) {
      _isPlay = true;
      _player.startPlayer(
        fromDataBuffer: (await rootBundle.load(audiourl)).buffer.asUint8List(),
        codec: Codec.mp3,
        whenFinished: () {
          // 播放完成回调
          _stopAudio();
          dismissDialog();
          widget.callBack.call();
        },
      );
    }
  }

  Future<void> _stopAudio() async {
    if (_isPlay) {
      await _player.stopPlayer();
      _isPlay = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 180.r,
      height: 180.r,
      child: SVGAImage(_animationController),
    );
  }
}
