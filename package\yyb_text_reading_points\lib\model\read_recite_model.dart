class ReadReciteModel {
  ReadReciteModel({
    required this.segmentType, //分段类型 1段 2首
    required this.reciteVo, //全文背诵、朗读内容
    required this.reciteVoList, //分段内容集合
    required this.readCount, //完成全文朗读人数
    required this.reciteCount, //完成全文背诵人数
    required this.readList, //朗读排行榜
    required this.reciteList, //背诵排行榜
    required this.moduleType, //1背 2读
  });

  final String? segmentType;
  final ReciteVo? reciteVo;
  final List<ReciteVoList> reciteVoList;
  final int? readCount;
  final int? reciteCount;
  final List<ReadListElement> readList;
  final List<ReadListElement> reciteList;
  final String? moduleType;

  ReadReciteModel copyWith({
    String? segmentType,
    ReciteVo? reciteVo,
    List<ReciteVoList>? reciteVoList,
    int? readCount,
    int? reciteCount,
    List<ReadListElement>? readList,
    List<ReadListElement>? reciteList,
    String? moduleType,
  }) {
    return ReadReciteModel(
      segmentType: segmentType ?? this.segmentType,
      reciteVo: reciteVo ?? this.reciteVo,
      reciteVoList: reciteVoList ?? this.reciteVoList,
      readCount: readCount ?? this.readCount,
      reciteCount: reciteCount ?? this.reciteCount,
      readList: readList ?? this.readList,
      reciteList: reciteList ?? this.reciteList,
      moduleType: moduleType ?? this.moduleType,
    );
  }

  factory ReadReciteModel.fromJson(Map<String, dynamic> json) {
    return ReadReciteModel(
      segmentType: json["segmentType"],
      reciteVo:
          json["reciteVo"] == null ? null : ReciteVo.fromJson(json["reciteVo"]),
      reciteVoList: json["reciteVoList"] == null
          ? []
          : List<ReciteVoList>.from(
              json["reciteVoList"]!.map((x) => ReciteVoList.fromJson(x))),
      readCount: json["readCount"],
      reciteCount: json["reciteCount"],
      readList: json["readList"] == null
          ? []
          : List<ReadListElement>.from(
              json["readList"]!.map((x) => ReadListElement.fromJson(x))),
      reciteList: json["reciteList"] == null
          ? []
          : List<ReadListElement>.from(
              json["reciteList"]!.map((x) => ReadListElement.fromJson(x))),
      moduleType: json["moduleType"],
    );
  }

  Map<String, dynamic> toJson() => {
        "segmentType": segmentType,
        "reciteVo": reciteVo?.toJson(),
        "reciteVoList": reciteVoList.map((x) => x.toJson()).toList(),
        "readCount": readCount,
        "reciteCount": reciteCount,
        "readList": readList.map((x) => x.toJson()).toList(),
        "reciteList": reciteList.map((x) => x.toJson()).toList(),
        "moduleType": moduleType,
      };

  @override
  String toString() {
    return "$segmentType, $reciteVo, $reciteVoList, $readCount, $reciteCount, $readList, $reciteList, $moduleType, ";
  }
}

class ReadListElement {
  ReadListElement({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userPhoto,
    required this.createDate,
    required this.score,
    required this.resultJson,
    required this.mp3Url,
    required this.sort,
  });

  final String? id;
  final String? userId;
  final String? userName;
  final String? userPhoto;
  final String? createDate;
  final int? score;
  final String? resultJson;
  final String? mp3Url;
  final int? sort;

  ReadListElement copyWith({
    String? id,
    String? userId,
    String? userName,
    String? userPhoto,
    String? createDate,
    int? score,
    String? resultJson,
    String? mp3Url,
    int? sort,
  }) {
    return ReadListElement(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userPhoto: userPhoto ?? this.userPhoto,
      createDate: createDate ?? this.createDate,
      score: score ?? this.score,
      resultJson: resultJson ?? this.resultJson,
      mp3Url: mp3Url ?? this.mp3Url,
      sort: sort ?? this.sort,
    );
  }

  factory ReadListElement.fromJson(Map<String, dynamic> json) {
    return ReadListElement(
      id: json["id"],
      userId: json["userId"],
      userName: json["userName"],
      userPhoto: json["userPhoto"],
      createDate: json["createDate"],
      score: json["score"],
      resultJson: json["resultJson"],
      mp3Url: json["mp3Url"],
      sort: json["sort"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "userId": userId,
        "userName": userName,
        "userPhoto": userPhoto,
        "createDate": createDate,
        "score": score,
        "resultJson": resultJson,
        "mp3Url": mp3Url,
        "sort": sort,
      };

  @override
  String toString() {
    return "$id, $userId, $userName, $userPhoto, $createDate, $score, $resultJson, $mp3Url, $sort, ";
  }
}

class ReciteVo {
  ReciteVo({
    required this.id,
    required this.moduleUnitConfigId,
    required this.reciteState,
    required this.content,
    required this.mp3Url,
    required this.mp3Time,
    required this.hasMp3,
    required this.reciteTime,
    required this.type,
    required this.userMp3Url,
    required this.userScore,
  });

  final String? id;
  final String? moduleUnitConfigId;
  final String? reciteState;
  final String? content;
  final String? mp3Url;
  final int? mp3Time;
  final String? hasMp3;
  final int? reciteTime;
  final String? type;
  final String? userMp3Url;
  final int? userScore;

  ReciteVo copyWith({
    String? id,
    String? moduleUnitConfigId,
    String? reciteState,
    String? content,
    String? mp3Url,
    int? mp3Time,
    String? hasMp3,
    int? reciteTime,
    String? type,
    String? userMp3Url,
    int? userScore,
  }) {
    return ReciteVo(
      id: id ?? this.id,
      moduleUnitConfigId: moduleUnitConfigId ?? this.moduleUnitConfigId,
      reciteState: reciteState ?? this.reciteState,
      content: content ?? this.content,
      mp3Url: mp3Url ?? this.mp3Url,
      mp3Time: mp3Time ?? this.mp3Time,
      hasMp3: hasMp3 ?? this.hasMp3,
      reciteTime: reciteTime ?? this.reciteTime,
      type: type ?? this.type,
      userMp3Url: userMp3Url ?? this.userMp3Url,
      userScore: userScore ?? this.userScore,
    );
  }

  factory ReciteVo.fromJson(Map<String, dynamic> json) {
    return ReciteVo(
      id: json["id"],
      moduleUnitConfigId: json["moduleUnitConfigId"],
      reciteState: json["reciteState"],
      content: json["content"],
      mp3Url: json["mp3Url"],
      mp3Time: json["mp3Time"],
      hasMp3: json["hasMp3"],
      reciteTime: json["reciteTime"],
      type: json["type"],
      userMp3Url: json["userMp3Url"],
      userScore: json["userScore"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "moduleUnitConfigId": moduleUnitConfigId,
        "reciteState": reciteState,
        "content": content,
        "mp3Url": mp3Url,
        "mp3Time": mp3Time,
        "hasMp3": hasMp3,
        "reciteTime": reciteTime,
        "type": type,
        "userMp3Url": userMp3Url,
        "userScore": userScore,
      };

  @override
  String toString() {
    return "$id, $moduleUnitConfigId, $reciteState, $content, $mp3Url, $mp3Time, $hasMp3, $reciteTime, $type, $userMp3Url, $userScore, ";
  }
}

class ReciteVoList {
  ReciteVoList({
    required this.id,
    required this.moduleUnitConfigId,
    required this.sort,
    required this.name,
    required this.content,
    required this.mp3Url,
    required this.mp3Time,
    required this.hasMp3,
    required this.reciteTime,
    required this.type,
    required this.textAlign,
    required this.author,
    required this.title,
    required this.authorAlign,
    required this.titleAlign,
    required this.userChecked,
    required this.readContent,
    required this.reciteState,
    required this.userScore,
  });

  final String? id;
  final String? moduleUnitConfigId;
  final int? sort;
  final String? name;
  final String? content;
  final String? mp3Url;
  final int? mp3Time;
  final String? hasMp3;
  final int? reciteTime;
  final String? type;
  final int? textAlign;
  final String? author;
  final String? title;
  final int? authorAlign;
  final int? titleAlign;
  final String? userChecked;
  final String? readContent;
  final String? reciteState;
  final int? userScore;

  ReciteVoList copyWith({
    String? id,
    String? moduleUnitConfigId,
    int? sort,
    String? name,
    String? content,
    String? mp3Url,
    int? mp3Time,
    String? hasMp3,
    int? reciteTime,
    String? type,
    int? textAlign,
    String? author,
    String? title,
    int? authorAlign,
    int? titleAlign,
    String? userChecked,
    String? readContent,
    String? reciteState,
    int? userScore,
  }) {
    return ReciteVoList(
      id: id ?? this.id,
      moduleUnitConfigId: moduleUnitConfigId ?? this.moduleUnitConfigId,
      sort: sort ?? this.sort,
      name: name ?? this.name,
      content: content ?? this.content,
      mp3Url: mp3Url ?? this.mp3Url,
      mp3Time: mp3Time ?? this.mp3Time,
      hasMp3: hasMp3 ?? this.hasMp3,
      reciteTime: reciteTime ?? this.reciteTime,
      type: type ?? this.type,
      textAlign: textAlign ?? this.textAlign,
      author: author ?? this.author,
      title: title ?? this.title,
      authorAlign: authorAlign ?? this.authorAlign,
      titleAlign: titleAlign ?? this.titleAlign,
      userChecked: userChecked ?? this.userChecked,
      readContent: readContent ?? this.readContent,
      reciteState: reciteState ?? this.reciteState,
      userScore: userScore ?? this.userScore,
    );
  }

  factory ReciteVoList.fromJson(Map<String, dynamic> json) {
    return ReciteVoList(
      id: json["id"],
      moduleUnitConfigId: json["moduleUnitConfigId"],
      sort: json["sort"],
      name: json["name"],
      content: json["content"],
      mp3Url: json["mp3Url"],
      mp3Time: json["mp3Time"],
      hasMp3: json["hasMp3"],
      reciteTime: json["reciteTime"],
      type: json["type"],
      textAlign: json["textAlign"],
      author: json["author"],
      title: json["title"],
      authorAlign: json["authorAlign"],
      titleAlign: json["titleAlign"],
      userChecked: json["userChecked"],
      readContent: json["readContent"],
      reciteState: json["reciteState"],
      userScore: json["userScore"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "moduleUnitConfigId": moduleUnitConfigId,
        "sort": sort,
        "name": name,
        "content": content,
        "mp3Url": mp3Url,
        "mp3Time": mp3Time,
        "hasMp3": hasMp3,
        "reciteTime": reciteTime,
        "type": type,
        "textAlign": textAlign,
        "author": author,
        "title": title,
        "authorAlign": authorAlign,
        "titleAlign": titleAlign,
        "userChecked": userChecked,
        "readContent": readContent,
        "reciteState": reciteState,
        "userScore": userScore,
      };

  @override
  String toString() {
    return "$id, $moduleUnitConfigId, $sort, $name, $content, $mp3Url, $mp3Time, $hasMp3, $reciteTime, $type, $textAlign, $author, $title, $authorAlign, $titleAlign, $userChecked, $readContent, $userScore, ";
  }
}
