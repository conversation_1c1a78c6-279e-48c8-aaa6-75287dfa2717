import { Log } from "@normalized:N&&&@ohos/flutter_ohos/index&1.0.0-299265ba05";
import type { FlutterEngine } from "@normalized:N&&&@ohos/flutter_ohos/index&1.0.0-299265ba05";
import AudioSessionPlugin from "@normalized:N&&&audio_session/index&1.0.0";
import AudioplayersPlugin from "@normalized:N&&&audioplayers_ohos/index&1.0.0";
import ConnectivityPlugin from "@normalized:N&&&connectivity_plus/index&1.0.0";
import DeviceInfoPlusOhosPlugin from "@normalized:N&&&device_info_plus/index&1.0.0";
import InAppWebViewFlutterPlugin from "@normalized:N&&&flutter_inappwebview_ohos/index&1.0.0";
import FlutterSoundPlugin from "@normalized:N&&&flutter_sound/index&1.0.0";
import FluwxPlugin from "@normalized:N&&&fluwx/index&1.0.0";
import ImagePickerPlugin from "@normalized:N&&&image_picker_ohos/index&1.0.0";
import JustAudioOhosPlugin from "@normalized:N&&&just_audio_ohos/index&1.0.0";
import MobileScannerPlugin from "@normalized:N&&&mobile_scanner/index&1.0.0";
import PackageInfoPlugin from "@normalized:N&&&package_info_plus/index&1.0.0";
import PathProviderPlugin from "@normalized:N&&&path_provider_ohos/index&1.0.0";
import PermissionHandlerPlugin from "@normalized:N&&&permission_handler_ohos/index&1.0.0";
import ScreenBrightnessOhosPlugin from "@normalized:N&&&screen_brightness_ohos/Index&1.0.0";
import SharedPreferencesPlugin from "@normalized:N&&&shared_preferences_ohos/index&1.0.0";
import UrlLauncherPlugin from "@normalized:N&&&url_launcher_ohos/index&1.0.0";
import VideoPlayerPlugin from "@normalized:N&&&video_player_ohos/Index&1.0.0";
const TAG = "GeneratedPluginRegistrant";
export class GeneratedPluginRegistrant {
    static registerWith(a: FlutterEngine) {
        try {
            a.getPlugins()?.add(new AudioSessionPlugin());
            a.getPlugins()?.add(new AudioplayersPlugin());
            a.getPlugins()?.add(new ConnectivityPlugin());
            a.getPlugins()?.add(new DeviceInfoPlusOhosPlugin());
            a.getPlugins()?.add(new InAppWebViewFlutterPlugin());
            a.getPlugins()?.add(new FlutterSoundPlugin());
            a.getPlugins()?.add(new FluwxPlugin());
            a.getPlugins()?.add(new ImagePickerPlugin());
            a.getPlugins()?.add(new JustAudioOhosPlugin());
            a.getPlugins()?.add(new MobileScannerPlugin());
            a.getPlugins()?.add(new PackageInfoPlugin());
            a.getPlugins()?.add(new PathProviderPlugin());
            a.getPlugins()?.add(new PermissionHandlerPlugin());
            a.getPlugins()?.add(new ScreenBrightnessOhosPlugin());
            a.getPlugins()?.add(new SharedPreferencesPlugin());
            a.getPlugins()?.add(new UrlLauncherPlugin());
            a.getPlugins()?.add(new VideoPlayerPlugin());
        }
        catch (b) {
            Log.e(TAG, "Tried to register plugins with FlutterEngine ("
                + a
                + ") failed.");
            Log.e(TAG, "Received exception while registering", b);
        }
    }
}
