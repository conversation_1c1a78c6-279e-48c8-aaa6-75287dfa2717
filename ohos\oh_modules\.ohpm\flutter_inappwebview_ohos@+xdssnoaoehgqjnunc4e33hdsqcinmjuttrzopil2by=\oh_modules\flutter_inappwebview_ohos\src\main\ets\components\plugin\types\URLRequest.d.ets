// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
export default class URLRequest {
    private url;
    private method;
    private body;
    private headers;
    constructor(d5: string, e5: string, f5: ArrayBuffer | null, g5: Map<string, string>);
    getUrl(): string;
    setUrl(c5: string): void;
    getMethod(): string;
    setMethod(b5: string): void;
    getBody(): ArrayBuffer | null;
    setBody(a5: ArrayBuffer): void;
    getHeaders(): Map<string, string>;
    setHeaders(z4: Map<string, string>): void;
    static fromMap(y4: Map<string, Any>): URLRequest | null;
    toMap(): Map<string, Any>;
}
