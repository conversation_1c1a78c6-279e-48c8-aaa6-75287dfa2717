// @keepTs
// @ts-nocheck
import ChannelDelegateImpl from '../types/ChannelDelegateImpl';
import PrintJobController from './PrintJobController';
import { MethodCall, MethodChannel } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
export default class PrintJobChannelDelegate extends ChannelDelegateImpl {
    private printJobController;
    constructor(y16: PrintJobController, z16: MethodChannel);
    onMethodCall(w16: MethodCall, x16: MethodResult): void;
    dispose(): void;
}
