// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
import ISettings from '../ISettings';
import { FindInteractionController } from './FindInteractionController';
export declare class FindInteractionSettings implements ISettings<FindInteractionController> {
    parse(o30: Map<string, Any>): ISettings<FindInteractionController>;
    toMap(): Map<string, Any>;
    getRealSettings(n30: FindInteractionController): Map<string, Any>;
}
