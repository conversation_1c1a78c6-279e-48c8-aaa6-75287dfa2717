// @keepTs
// @ts-nocheck
import { FlutterSoundRecorderCallback } from '../plugin/FlutterSoundRecorderCallback';
import { t_AUDIO_SOURCE, t_CODEC, t_RECORDER_STATE } from '../plugin/FlutterSoundTypes';
export declare class FlutterRecorder {
    private recorder;
    m_callback: FlutterSoundRecorderCallback | null;
    private m_path;
    private subsDurationMillis;
    private intervalID;
    private mPauseTime;
    private mStartPauseTime;
    private mStartTime;
    private status;
    constructor(n6: FlutterSoundRecorderCallback);
    openRecorder(): Promise<boolean>;
    closeRecorder(): Promise<void>;
    isEncoderSupported(m6: t_CODEC): boolean;
    getRecorderState(): t_RECORDER_STATE;
    deleteRecord(l6: string): Promise<boolean>;
    private clearTimer;
    private setTimer;
    startRecorder(e6: t_CODEC, f6: number, g6: number, h6: number, i6: string, j6: t_AUDIO_SOURCE, k6: boolean): Promise<boolean>;
    recordingData(d6: ArrayBuffer): void;
    stopRecorder(): Promise<void>;
    pauseRecorder(): Promise<void>;
    resumeRecorder(): Promise<void>;
    setSubsDurationMillis(c6: number): void;
    temporayFile(b6: string): string;
    private getPath;
    private stop;
}
