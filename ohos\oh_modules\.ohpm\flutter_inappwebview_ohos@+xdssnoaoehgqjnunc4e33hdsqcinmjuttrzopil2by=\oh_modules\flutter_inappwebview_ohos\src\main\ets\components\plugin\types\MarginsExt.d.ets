// @keepTs
// @ts-nocheck
export default class MarginsExt {
    private top;
    private right;
    private bottom;
    private left;
    constructor(g17: number, h17: number, i17: number, j17: number);
    static fromMap(f17: Map<String, Object>): MarginsExt | null;
    private milsToPixels;
    private pixelsToMils;
    toMap(): Map<String, Object>;
    getTop(): number;
    setTop(e17: number): void;
    getRight(): number;
    setRight(d17: number): void;
    getBottom(): number;
    setBottom(c17: number): void;
    getLeft(): number;
    setLeft(b17: number): void;
    equals(a17: Object): boolean;
    hashCode(): number;
    toString(): string;
}
