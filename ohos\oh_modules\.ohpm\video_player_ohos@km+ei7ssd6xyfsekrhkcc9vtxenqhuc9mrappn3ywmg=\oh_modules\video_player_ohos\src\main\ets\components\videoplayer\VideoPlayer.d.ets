// @keepTs
// @ts-nocheck
import media from '@ohos.multimedia.media';
import resourceManager from '@ohos.resourceManager';
import { SurfaceTextureEntry } from '@ohos/flutter_ohos/src/main/ets/view/TextureRegistry';
import { PlayerModel } from './PlayerModel';
import { EventChannel } from '@ohos/flutter_ohos';
export declare class VideoPlayer {
    private avPlayer;
    playerModel: PlayerModel | null;
    private duration;
    private status;
    private loop;
    private index;
    private rawFile?;
    private url;
    private surfaceId;
    private seekTime;
    private positionX;
    private positionY;
    private textureEntry;
    private eventChannel;
    private eventSink;
    private interruptMode;
    private fd;
    private headers;
    constructor(a: PlayerModel, b: SurfaceTextureEntry, c: resourceManager.RawFileDescriptor | null, d: string | null, e: EventChannel, f: Boolean, g: Record<string, string> | null);
    /**
     * Creates a videoPlayer object.
     */
    createAVPlayer(): Promise<void>;
    /**
     * AVPlayer binding event.
     */
    bindState(): Promise<void>;
    /**
     * Release the video player.
     */
    release(): void;
    play(): void;
    /**
     * Pause Playing.
     */
    pause(): void;
    seekTo(s4: number): void;
    getPosition(): number;
    /**
     * Playback mode. The options are as follows: true: playing a single video; false: playing a cyclic video.
     */
    setLoop(): void;
    setLooping(r4: boolean): void;
    setVolume(q4: number): void;
    /**
     * Set the playback speed.
     *
     * @param playSpeed Current playback speed.
     */
    setSpeed(p4: number): void;
    /**
     * Previous video.
     */
    previousVideo(): void;
    /**
     * Next video.
     */
    nextVideo(): void;
    /**
     * Switching Between Video Play and Pause.
     */
    switchPlayOrPause(): void;
    /**
     * Slide the progress bar to set the playback progress.
     *
     * @param value Value of the slider component.
     * @param mode Slider component change event.
     */
    setSeekTime(j4: number, k4: SliderChangeMode): void;
    /**
     * Setting the brightness.
     */
    setBright(): void;
    /**
     * Obtains the current video playing status.
     */
    getStatus(): number;
    /**
     * Initialization progress bar.
     *
     * @param time Current video playback time.
     */
    initProgress(f4: number): void;
    /**
     * Reset progress bar data.
     */
    resetProgress(): void;
    /**
     * Volume gesture method onActionStart.
     *
     * @param event Gesture event.
     */
    onVolumeActionStart(e4?: GestureEvent): void;
    /**
     * Bright gesture method onActionStart.
     *
     * @param event Gesture event.
     */
    onBrightActionStart(d4?: GestureEvent): void;
    /**
     * Gesture method onActionUpdate.
     *
     * @param event Gesture event.
     */
    onVolumeActionUpdate(w3?: GestureEvent): void;
    /**
     * Gesture method onActionUpdate.
     *
     * @param event Gesture event.
     */
    onBrightActionUpdate(p3?: GestureEvent): void;
    /**
     * Gesture method onActionEnd.
     */
    onActionEnd(): void;
    /**
     * Sets whether the screen is a constant based on the playback status.
     */
    watchStatus(): void;
    /**
     * Sets the playback page size based on the video size.
     */
    setVideoSize(): void;
    /**
     * An error is reported during network video playback.
     */
    playError(): void;
    sendInitialized(): void;
    sendCompleted(): void;
    sendBufferingUpdate(h3: media.BufferingInfoType, i3: number): void;
    sendError(g3: Object): void;
    getIUri(): string;
}
