// @keepTs
// @ts-nocheck
import ArrayList from '@ohos.util.ArrayList';
import common from '@ohos.app.ability.common';
import ImagePickerCache from './ImagePickerCache';
import FileUtils from './FileUtils';
import { ImageSelectionOptions, VideoSelectionOptions, Result, CacheRetrievalResult, MediaSelectionOptions, GeneralOptions } from './Messages';
import ImageResizer from './ImageResizer';
import UIAbility from '@ohos.app.ability.UIAbility';
import photoAccessHelper from "@ohos.file.photoAccessHelper";
export default class ImagePickerDelegate {
    readonly REQUEST_CODE_CHOOSE_IMAGE_FROM_GALLERY = 2342;
    readonly REQUEST_CODE_TAKE_IMAGE_WITH_CAMERA = 2343;
    readonly REQUEST_CAMERA_IMAGE_PERMISSION = 2345;
    readonly REQUEST_CODE_CHOOSE_MULTI_IMAGE_FROM_GALLERY = 2346;
    readonly REQUEST_CODE_CHOOSE_MEDIA_FROM_GALLERY = 2347;
    readonly REQUEST_CODE_CHOOSE_VIDEO_FROM_GALLERY = 2352;
    readonly REQUEST_CODE_TAKE_VIDEO_WITH_CAMERA = 2353;
    readonly REQUEST_CAMERA_VIDEO_PERMISSION = 2355;
    private static TAG;
    private imageResizer;
    private cache;
    private pendingCameraMediaUri;
    private pendingCallState;
    private context;
    private photoPicker;
    private cameraPosition;
    constructor(d5: UIAbility, e5: ImageResizer, f5: ImagePickerCache, g5?: common.UIAbilityContext, h5?: ImageSelectionOptions, i5?: VideoSelectionOptions, j5?: Result<ArrayList<string>>, k5?: FileUtils);
    setCameraDevice(c5: CameraDevice): void;
    saveStateBeforeResult(): void;
    retrieveLostImage(): Promise<CacheRetrievalResult | null>;
    chooseMedia(z4: number, a5: string, b5?: photoAccessHelper.PhotoViewMIMETypes): void;
    handleResultType(w4: string, x4: number, y4: Array<string>): void;
    chooseMediaFromGallery(t4: MediaSelectionOptions, u4: GeneralOptions, v4: Result<ArrayList<string>>): void;
    handleChooseMediaResult(r4: number, s4: Array<string>): void;
    handleMediaResultTwo(q4: Array<string>): Promise<void>;
    chooseVideoFromGallery(n4: VideoSelectionOptions, o4: boolean, p4: Result<ArrayList<string>>): void;
    handleChooseVideoResult(l4: number, m4: Array<string>): void;
    takeVideoWithCamera(j4: VideoSelectionOptions, k4: Result<ArrayList<string>>): void;
    launchTakeVideoWithCameraWant(i4: number): Promise<void>;
    private openOrCreateFile;
    private getDisplayName;
    chooseImageFromGallery(f4: ImageSelectionOptions, g4: boolean, h4: Result<ArrayList<string>>): void;
    handleChooseImageResult(d4: number, e4: Array<string>): void;
    chooseMultiImagesFromGallery(a4: ImageSelectionOptions, b4: boolean, c4: Result<ArrayList<string>>): void;
    takeImageWithCamera(y3: ImageSelectionOptions, z3: Result<ArrayList<string>>): void;
    launchTakeImageWithCameraWant(): void;
    setPendingOptionsAndResult(v3: ImageSelectionOptions | null, w3: VideoSelectionOptions | null, x3: Result<ArrayList<string>>): boolean;
    finishWithAlreadyActiveError(u3: Result<ArrayList<string>>): void;
    handlerCaptureImageResult(s3: number, t3: string): void;
    handlerCaptureVideoResult(q3: number, r3: string): void;
    handleImageResult(o3: string, p3: boolean): Promise<void>;
    handleMediaResult(n3: Array<string>): Promise<void>;
    handleVideoResult(l3: string | null, m3: boolean): void;
    finishWithSuccess(k3: string | null): void;
    finishWithListSuccess(j3: ArrayList<string> | null): void;
    getResizedImagePath(h3: string, i3: ImageSelectionOptions): Promise<string>;
}
export declare enum CameraDevice {
    REAR = 0,
    FRONT = 1
}
