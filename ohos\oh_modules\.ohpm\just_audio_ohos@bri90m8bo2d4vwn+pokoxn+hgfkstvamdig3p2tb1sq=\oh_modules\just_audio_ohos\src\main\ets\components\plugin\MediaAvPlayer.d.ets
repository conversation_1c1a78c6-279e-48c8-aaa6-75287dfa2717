// @keepTs
// @ts-nocheck
/**
 * Copyright (C) 2024 Huawei Device Co., Ltd.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 * */
import media from "@ohos.multimedia.media";
import MediaSource from './MediaSource';
import { MethodResult } from '@ohos/flutter_ohos';
declare enum AudioPlayerState {
    IDLE = 0,
    INITIALIZED = 1,
    LOAD = 2,
    PREPARED = 3,
    PLAY = 4,
    PAUSE = 5,
    STOP = 6,
    ERROR = 7,
    COMPLETED = 8,
    RELEASED = 9,
    PROGRESS_SPEED = 10,
    TIME_UPDATE = 11,
    VOLUME_CHANGE = 12,
    UNKNOWN = 13
}
export declare enum LoopMode {
    DEFAULT = 0,
    SINGLE_CYCLE = 1,
    LIST_LOOP = 2
}
export declare enum PlaybackState {
    STATE_READY = 0,
    STATE_BUFFERING = 1,
    STATE_ENDED = 2
}
export interface onChangeMediaAvPlayerState {
    onPlaybackStateChanged(state: PlaybackState): void;
    onUpdateCurrentIndex(updateIndex: number): void;
    onUpdatePosition(updatePosition: number): void;
    onUpdateBufferedPosition(bufferedPosition: number): void;
    onAudioSessionIdChanged(audioSessionId: number): void;
    broadcastImmediatePlaybackEvent(): void;
    completeSeek(): void;
    completeInitial(): void;
    onStartBuffering(): void;
    onEndBuffering(): void;
    setAudioSessionId(audioSessionId: number): void;
}
export declare class MediaAvPlayer {
    private context;
    avPlayer: media.AVPlayer | null;
    avPlayerNext: media.AVPlayer | null;
    isPlaying: boolean;
    songList: MediaSource[];
    state: AudioPlayerState;
    private session?;
    private initialPos;
    private initialIndex;
    private volume;
    private speed;
    private seekModule;
    private playMode;
    private isFirst;
    private isPrepared;
    private isClipping;
    private isPreBuffering;
    private isRandom;
    private musicIndex;
    private preMusicIndex;
    private songItem;
    private shuffleOrder;
    private avMetadataExtractor;
    private avMetadata?;
    private mediaAvPlayerStateChangeListener?;
    private playStrategy;
    constructor();
    setMediaAvPlayerStateChange(l1: onChangeMediaAvPlayerState): void;
    /**
     * 获取MediaAvPlayer实例
     * */
    static getInstance(): MediaAvPlayer;
    private playCall;
    private pauseCall;
    private playNextCall;
    private playPreviousCall;
    /**
     * 异常监听
     * */
    private errorCall;
    /**
     * 缓冲回调
     * */
    private bufferingUpdate;
    /**
     * 下一曲缓冲回调
     * */
    private bufferingUpdateNext;
    /**
     * 监听资源播放当前时间，单位为毫秒（ms），用于刷新进度条当前位置，默认间隔100ms时间上报，因用户操作(seek)产生的时间变化会立刻上报
     * */
    private updateTimeCall;
    /**
     * 进度条拖动回调,拖动松手后不会立刻回调,完成后回调
     * */
    private seekCall;
    /**
     *订阅录制状态机AVRecorderState切换的事件，当 AVRecorderState状态机发生变化时，会通过订阅的回调方法通知用户。用户只能订阅一个状态机切换事件的回调方法，当用户重复订阅时，以最后一次订阅的回调接口为准
     * */
    private stateCall;
    /**
     *订阅录制状态机AVRecorderState切换的事件，当 AVRecorderState状态机发生变化时，会通过订阅的回调方法通知用户。用户只能订阅一个状态机切换事件的回调方法，当用户重复订阅时，以最后一次订阅的回调接口为准
     * */
    private preStateCall;
    playingState(): void;
    setAVPlayerNextCallback(): void;
    offAVPlayerNextCallback(): void;
    setListenerForMesFromController(): void;
    /**
     * 为服务设置回调监听
     * */
    private setAVPlayerCallback;
    /**
     * 为服务注销回调监听
     * */
    private offAVPlayerCallback;
    /**
     * Play previous music.
     */
    playPrevious(): void;
    private updateMusicIndex;
    setAVMetadata(): Promise<void>;
    getDuration(): number;
    setLoopMode(k1: number): void;
    setShuffleMode(j1: boolean): void;
    setVolume(i1: number): void;
    setSpeed(h1: number): void;
    /**
     * 初始化音频播放器
     * */
    initAudioPlayer(): Promise<void>;
    createSession(): Promise<void>;
    /**
     * Play music.
     */
    play(g1?: MethodResult): Promise<void>;
    /**
     * Pause music.
     */
    pause(f1?: MethodResult): void;
    /**
     * Play next music.
     *
     * @param isFromControl
     */
    playNextAuto(): void;
    /**
     * Play music by index.
     *
     * @param musicIndex
     */
    loadAssent(e1: number): Promise<void>;
    dispose(): void;
    private playNext;
    private playRandom;
    /**
     * Seek play music.
     *
     * @param ms.
     */
    seek(c1: number, d1?: number): void;
    private loadUri;
    addPublicMediaSources(a1: number, b1: Array<MediaSource>): void;
    removeMediaSourceRange(y: number, z: number): Promise<void>;
    moveMediaSource(w: number, x: number): void;
    private updateCurrentIndex;
    setShuffleOrder(v: number[]): void;
    load(s: MediaSource, t: number, u?: number): Promise<void>;
    stop(): void;
    private start;
    private updateIsPlay;
    getCurrentTime(): number;
    private setPlayState;
}
export {};
