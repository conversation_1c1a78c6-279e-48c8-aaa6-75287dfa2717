// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
export default class HttpAuthResponse {
    private username;
    private password;
    permanentPersistence: boolean;
    private action;
    constructor(m23: string, n23: string, o23: boolean, p23: number | null);
    static fromMap(l23: Map<string, Any>): HttpAuthResponse | null;
    getUsername(): string;
    setUsername(k23: string): void;
    getPassword(): string;
    setPassword(j23: string): void;
    isPermanentPersistence(): boolean;
    setPermanentPersistence(i23: boolean): void;
    getAction(): number | null;
    setAction(h23: number | null): void;
    toString(): string;
}
