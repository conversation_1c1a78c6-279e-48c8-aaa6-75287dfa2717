// @keepTs
// @ts-nocheck
import common from '@ohos.app.ability.common';
import AbilityAware from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/ability/AbilityAware';
import { AbilityPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/ability/AbilityPluginBinding';
import { FlutterPlugin, FlutterPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import { PathProviderApi, StorageDirectory } from './Messages';
export default class PathProviderPlugin extends PathProviderApi implements FlutterPlugin, AbilityAware {
    private pluginBinding;
    private context;
    constructor(n?: common.Context);
    getUniqueClassName(): string;
    onAttachedToEngine(m: FlutterPluginBinding): void;
    onDetachedFromEngine(l: FlutterPluginBinding): void;
    onAttachedToAbility(k: AbilityPluginBinding): void;
    onDetachedFromAbility(): void;
    static registerWith(): void;
    setup(i: BinaryMessenger, j: common.Context): void;
    getTemporaryPath(): string;
    getApplicationSupportPath(): string;
    getApplicationDocumentsPath(): string;
    getApplicationCachePath(): string;
    getExternalStoragePath(): string;
    getExternalCachePaths(): Array<string>;
    getExternalStoragePaths(h: StorageDirectory): Array<string>;
    private getPathProviderTemporaryDirectory;
    private getApplicationSupportDirectory;
    private getPathProviderApplicationDocumentsDirectory;
    private getPathProviderStorageDirectory;
    private getPathProviderExternalCacheDirectories;
    private getStorageDirectoryString;
    private getPathProviderExternalStorageDirectories;
}
