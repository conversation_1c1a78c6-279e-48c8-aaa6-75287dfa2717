// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
export default class URLCredential {
    private id;
    private username;
    private password;
    private protectionSpaceId;
    constructor(u26: number | null, v26: string | null, w26: string | null, x26: number | null);
    toMap(): Map<string, Any>;
    getId(): number | null;
    setId(t26: number | null): void;
    getUsername(): string | null;
    setUsername(s26: string | null): void;
    getPassword(): string | null;
    setPassword(r26: string | null): void;
    getProtectionSpaceId(): number | null;
    setProtectionSpaceId(q26: number | null): void;
    toString(): string;
}
