// @keepTs
// @ts-nocheck
import CredentialDatabaseHelper from './CredentialDatabaseHelper';
import URLCredentialDao from './URLCredentialDao';
import URLProtectionSpaceDao from './URLProtectionSpaceDao';
import common from '@ohos.app.ability.common';
import URLCredential from '../types/URLCredential';
import List from '@ohos.util.List';
export default class CredentialDatabase {
    private static instance;
    static DATABASE_VERSION: number;
    static DATABASE_NAME: string;
    protectionSpaceDao: URLProtectionSpaceDao;
    credentialDao: URLCredentialDao;
    db: CredentialDatabaseHelper;
    constructor(s28: CredentialDatabaseHelper, t28: URLProtectionSpaceDao, u28: URLCredentialDao);
    static getInstance(r28: common.Context): CredentialDatabase;
    getHttpAuthCredentials(n28: string, o28: string, p28: string, q28: number): Promise<List<URLCredential>>;
    setHttpAuthCredential(h28: string, i28: string, j28: string, k28: number, l28: string, m28: string): Promise<void>;
    removeHttpAuthCredentials(d28: string, e28: string, f28: string, g28: number): Promise<void>;
    removeHttpAuthCredential(x27: string, y27: string, z27: string, a28: number, b28: string, c28: string): Promise<void>;
    clearAllAuthCredentials(): void;
}
