// @keepTs
// @ts-nocheck
import { EventSink } from '@ohos/flutter_ohos/src/main/ets/plugin/common/EventChannel';
export declare class QueuingEventSink implements EventSink {
    private delegate;
    private eventQueue;
    private done;
    setDelegate(k2: EventSink | null): void;
    success(j2: Object): void;
    error(g2: string, h2: string, i2: Object): void;
    endOfStream(): void;
    private enqueue;
    maybeFlush(): void;
}
