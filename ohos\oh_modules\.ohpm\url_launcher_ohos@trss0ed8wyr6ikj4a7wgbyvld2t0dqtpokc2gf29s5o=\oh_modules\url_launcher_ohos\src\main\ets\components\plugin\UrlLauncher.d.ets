// @keepTs
// @ts-nocheck
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import MessageCodec from '@ohos/flutter_ohos/src/main/ets/plugin/common/MessageCodec';
import { UrlLauncherApi, WebViewOptions } from './Messages';
import common from '@ohos.app.ability.common';
export declare class UrlLauncher implements UrlLauncherApi {
    static LAUNCH_TYPE_TEL: string;
    static LAUNCH_TYPE_WEB_HTTP: string;
    static LAUNCH_TYPE_WEB_HTTPS: string;
    static LAUNCH_TYPE_MAILTO: string;
    static LAUNCH_TYPE_SMS: string;
    static LAUNCH_TYPE_FILE: string;
    static LAUNCH_TYPE_APP_GALLERY: string;
    static MMS_BUNDLE_NAME: string;
    static MMS_ABILITY_NAME: string;
    static MMS_ENTITIES: string;
    private context;
    constructor(e1: common.UIAbilityContext);
    getPermission(): void;
    canLaunchUrl(d1: string): boolean;
    launchUrl(b1: string, c1: Map<string, string>): boolean;
    format(a1: string): string;
    launchSms(z: string): boolean;
    launchTel(y: string): boolean;
    launchFile(x: string): boolean;
    launchWeb(v: string, w: Map<string, string>): boolean;
    launchMail(u: string): boolean;
    /**
     * 跳转到应用商店详情页
     * 文档参考：https://developer.huawei.com/consumer/cn/doc/harmonyos-faqs/faqs-ability-kit#section42001122242
     * @param url
     * @returns
     */
    launchAppGallery(t: string): boolean;
    launchOther(s: string): boolean;
    parseUrl(q: string, r: string): string;
    private ensureContext;
    openUrlInWebView(o: string, p: WebViewOptions): boolean;
    closeWebView(): boolean;
    getCodec(): MessageCodec<ESObject>;
    setup(m: BinaryMessenger, n: UrlLauncherApi): void;
}
