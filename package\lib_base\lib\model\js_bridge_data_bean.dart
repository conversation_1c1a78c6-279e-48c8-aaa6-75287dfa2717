import 'package:json_annotation/json_annotation.dart';
import 'package:lib_base/model/http/math_video_bean.dart';
import 'package:lib_base/model/pan_listen_audio_res_info.dart';
import 'package:lib_base/model/s_n_d_d_audio_compilation_info.dart';
import 'package:lib_base/utils/json_utils.dart';

part 'js_bridge_data_bean.g.dart';

@JsonSerializable(includeIfNull: false)
class JsBridgeDataBean {
  bool? isHeader;
  String? url;
  bool? isFinish;
  String? key;
  String? iv;
  dynamic? value;
  dynamic? type;
  int? code;
  String? content;
  String? contentType;
  dynamic? id;
  dynamic? seriesId;
  String? seriesName;
  dynamic? schoolId;
  String? title;
  String? payInfo;
  String? summary;
  String? shareUrl;
  String? imageUrl;
  String? result;
  dynamic? courseId;
  dynamic? periodId;
  dynamic? ccUserId;
  String? periodName;
  dynamic? roomId;
  dynamic? replyId;
  dynamic? liveId;
  String? courseName;
  dynamic? appId;
  String? appName;
  String? brandName;
  /**
   * 应用图标
   */
  String? iconLinkUrl;

  /**
   * 下载二维码
   */
  String? ewmLinkUrl;

  // ZybDetailItem reportObj;
  bool? hasVideo;
  bool? hasFull;
  dynamic? activityId;
  dynamic? taskType;
  dynamic? taskCode;
  dynamic? resourceId;
  dynamic? answerId;
  dynamic? competitionNum;
  dynamic? userId;
  dynamic? userName;
  dynamic? memberId;
  dynamic? autoPlay;
  dynamic? parentId;
  dynamic? clazzId;
  dynamic? holidayType;
  dynamic? holidayId;
  dynamic? clazzName;
  dynamic? studentName;
  dynamic? holidayName;
  dynamic? startTime;
  dynamic? endTime;
  dynamic? from;
  bool? hasStatus;
  dynamic? activeityId;
  dynamic? activeityImg;
  dynamic? homeworkId;
  dynamic? studentId;
  dynamic? detailId;
  /**
   * 作业类型
   */
  dynamic? mHomeworkType;
  bool? shouldWebBack;
  dynamic? pageName;
  dynamic? pageParam;
  bool? isLandscape;
  dynamic? subject;
  dynamic? time;
  dynamic? coreType;
  dynamic? score;
  dynamic? file;
  dynamic? cover;
  bool? hasEvaluat;
  dynamic? moduleConfigId;
  dynamic? unitId;
  dynamic? breakthroughId;
  dynamic? questionNum;
  dynamic? upVal;
  dynamic? questionId;
  dynamic? hasLevel;
  dynamic? unitName;
  dynamic? examPaperId;
  dynamic? examId;
  dynamic? examDuration;
  dynamic? accuracy;
  dynamic? examTypeName;
  dynamic? jointExamineName;
  dynamic? examName;
  dynamic? areaCode;
  double? totalScore;
  dynamic? finishRate;
  dynamic? source;
  dynamic? category;
  dynamic? levelName;
  dynamic? gearName;
  dynamic? num;
  dynamic? dedicatedRegion;
  dynamic? wrongExamType;
  dynamic? tfRate;
  // BookInfoItem bookInfoItem;
  dynamic? moduleName;
  dynamic? checkStatus;
  bool? isfinish;
  dynamic? articleId;
  dynamic? ladderReadId;
  int? height;
  int? index;
  dynamic? sentence;
  dynamic? eventName;
  dynamic? eventData;
  dynamic? chinese;
  dynamic? english;
  dynamic? math;
  dynamic? grade;
  dynamic? gradeName;
  dynamic? studyPlanId;
  dynamic? dayId;
  dynamic? stage;
  dynamic? state;
  dynamic? medalId;
  dynamic? days;
  dynamic? classmateId;
  dynamic? classmateDayId;
  dynamic? pictureBookId;
  dynamic? planModuleId;
  dynamic? moduleType;
  dynamic? knowledgeId;
  dynamic? resourceName;
  dynamic? reviewModuleId;
  dynamic? clazzCode;
  dynamic? phoneNum;
  dynamic? bookId;
  dynamic? wordId;
  dynamic? version;
  dynamic? fascicule;
  dynamic enTime;
  dynamic? en;
  Map<String, dynamic>? kv;
  dynamic? name;
  @JsonKey(fromJson: JsonUtils.parseToString)
  String? isUpload;
  bool? backBool;
  int? deviceModel;
  dynamic? provinceCity;
  @JsonKey(fromJson: JsonUtils.parseToString)
  String? qyType;
  dynamic? questionIds;
  dynamic? startDateStr;
  StuHomeworkRecBean? recBean;
  dynamic? labelId;
  dynamic? reviewId;
  dynamic? subId;
  int? levelNum;
  int? quantity;
  int? freeCount;
  dynamic? rankName;
  dynamic? selectLever;
  dynamic? res;
  dynamic? bookInfoItem;

  /**
   * 数据流数组
   */
  // byte[] imgData;

  /**
   * 企业微信二维码
   */
  String? qwQRCode;
  /**
   * 作业信息
   */
  // PracticeInfo mHomeworkInfo;
  // MathVideoBean.ResultVoListBean.InfoVoList infoVo;

  /**
   * 学生名单信息列表
   */
  // List<StuUserInfo> mStuList;
  //
  /**
   * 布置泛读数据
   */
  // List<BzzyDubbingBean> discReadList;

  /**
   * 需提醒学生Id列表
   */
  // List<String> mRemindList;

  /**
   * 学生详情信息
   */
  // StudentHomeworkCompleteItem mStudentInfo;

  /**
   * 直播信息
   */
  // LivePlayerInfo mLivePlayerInfo;

  /**
   * 学习计划-练习题目
   */
  // List<QuestionItem> mQuestionList;

  /**
   * 学习计划-单词听写
   */
  // List<WordDictationItem> mWordDictationList;

  /**
   * 少年得到-音频合辑信息
   */
  SNDDAudioCompilationInfo? compilation;

  /**
   * 少年得到-音频资源信息
   */
  List<PanListenAudioResInfo>? resourceList;

  /**
   * 训练营Id
   */
  dynamic? trainingCampId;

  /**
   * 营期Id
   */
  dynamic? tcManageId;

  /**
   * 主题Id
   */
  dynamic? manageId;

  //底部bar的高度
  double? tasselHeight;

  /**
   * e豆
   */
  int? eDou;

  List? unstowedList;

  String? soundText;

 InfoVoList? infoVo;

  JsBridgeDataBean();

  factory JsBridgeDataBean.fromJson(
    Map<String, dynamic> json,
  ) =>
      _$JsBridgeDataBeanFromJson(json);

  Map<String, dynamic> toJson() => _$JsBridgeDataBeanToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}

@JsonSerializable(includeIfNull: false)
class StuHomeworkRecBean {
  String? id;
  String? name;
  String? remark;
  String? image;
  String? advType;
  String? jumpType;
  String? androidLinkUrl;
  String? iosLinkUrl;
  String? h5LinkUrl;
  String? paramSet;
  String? subject;
  String? grade;
  String? locUnit;
  String? segment;
  String? version;
  String? fascicule;
  String? bookId;
  String? recommendType;
  String? operationName;
  String? type;
  String? bigImage;
  List<StuHomeworkRecBean>? list;

  StuHomeworkRecBean(); // List<StuHomeworkRecBean>? list;

  factory StuHomeworkRecBean.fromJson(
    Map<String, dynamic> json,
  ) =>
      _$StuHomeworkRecBeanFromJson(json);

  Map<String, dynamic> toJson() => _$StuHomeworkRecBeanToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}
