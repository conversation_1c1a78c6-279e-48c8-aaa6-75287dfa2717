import 'package:json_annotation/json_annotation.dart';
import 'package:lib_base/model/http/math_video_bean.dart';

part 'math_dict_list_bean.g.dart';

@JsonSerializable()
class MathDictListBean {
  final String? value;
  final String? label;
  final String? moduleType;
  final String? bookId;
  final bool? isSelect;
  final List<DictList>? dictList;

  const MathDictListBean({
    this.value,
    this.label,
    this.moduleType,
    this.bookId,
    this.isSelect,
    this.dictList,
  });

  factory MathDictListBean.fromJson(Map<String, dynamic> json) =>
      _$MathDictListBeanFromJson(json);

  Map<String, dynamic> toJson() => _$MathDictListBeanToJson(this);
}

@JsonSerializable()
class DictList {
  final String? value;
  final String? label;
  final String? bookId;
  final bool? isSelect;
  final List<MathVideoBeanResultVoListBeanInfoVoList>? videoList;

  const DictList({
    this.value,
    this.label,
    this.bookId,
    this.isSelect,
    this.videoList,
  });

  factory DictList.fromJson(Map<String, dynamic> json) =>
      _$DictListFromJson(json);

  Map<String, dynamic> toJson() => _$DictListToJson(this);
}