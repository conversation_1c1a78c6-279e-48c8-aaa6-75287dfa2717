# Android代码转Dart逻辑任务模板

## 使用方式
只需提供以下参数即可执行任务：

### 必需参数
1. **Android代码片段**：完整的Android代码片段（Java/Kotlin）
2. **任务背景说明**：代码的功能描述、使用场景、业务逻辑等

### 可选参数
3. **目标Dart文件路径**：如果有特定的文件路径要求
4. **依赖信息**：相关的类、接口、工具类等依赖信息
5. **特殊要求**：性能要求、兼容性要求等

### 使用示例

#### 基础使用
```
请根据ai_task/android_to_dart_task.md里面的任务描述执行任务

Android代码片段：
public class UserManager {
    private static UserManager instance;
    private String userId;
    private String userName;
    
    public static UserManager getInstance() {
        if (instance == null) {
            instance = new UserManager();
        }
        return instance;
    }
    
    public void setUserInfo(String userId, String userName) {
        this.userId = userId;
        this.userName = userName;
        saveToPreferences();
    }
    
    private void saveToPreferences() {
        SharedPreferences prefs = context.getSharedPreferences("user_prefs", Context.MODE_PRIVATE);
        prefs.edit()
            .putString("user_id", userId)
            .putString("user_name", userName)
            .apply();
    }
}

任务背景说明：
这是一个用户信息管理的单例类，用于在应用中管理用户的基本信息（用户ID和用户名），并将信息持久化到SharedPreferences中。主要用于登录后保存用户信息，以及在应用的其他地方获取当前用户信息。
```

#### 高级使用（包含依赖和特殊要求）
```
请根据ai_task/android_to_dart_task.md里面的任务描述执行任务

Android代码片段：
public class NetworkUtil {
    private static final String TAG = "NetworkUtil";
    
    public static boolean isNetworkAvailable(Context context) {
        ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (cm != null) {
            NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
            return activeNetwork != null && activeNetwork.isConnectedOrConnecting();
        }
        return false;
    }
    
    public static String getNetworkType(Context context) {
        ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (cm != null) {
            NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
            if (activeNetwork != null) {
                switch (activeNetwork.getType()) {
                    case ConnectivityManager.TYPE_WIFI:
                        return "WIFI";
                    case ConnectivityManager.TYPE_MOBILE:
                        return "MOBILE";
                    default:
                        return "OTHER";
                }
            }
        }
        return "NONE";
    }
}

任务背景说明：
这是一个网络状态检测工具类，用于检测设备的网络连接状态和网络类型。在应用中用于判断是否有网络连接，以及当前使用的是WiFi还是移动网络，用于优化网络请求策略。

依赖信息：
- 需要使用Flutter的connectivity_plus插件来获取网络状态
- 可能需要添加网络权限检查

特殊要求：
- 需要支持异步操作
- 需要考虑不同平台（Android/iOS）的兼容性
- 建议添加网络状态变化的监听功能
```

---

## 自动化实现逻辑

### 1. Android代码分析
自动分析Android代码片段，识别：
- **类结构**：类名、方法名、字段名
- **设计模式**：单例、工厂、观察者等
- **Android特有API**：SharedPreferences、Context、系统服务等
- **业务逻辑**：核心功能和数据流
- **依赖关系**：外部类、接口、工具类的使用

### 2. Dart语法转换规则

#### 2.1 基础类型映射
- `String` → `String`
- `int` → `int`
- `long` → `int`
- `float` → `double`
- `double` → `double`
- `boolean` → `bool`
- `void` → `void`
- `Object` → `dynamic` 或具体类型

#### 2.2 集合类型映射
- `List<T>` → `List<T>`
- `Map<K,V>` → `Map<K,V>`
- `Set<T>` → `Set<T>`
- `Array[]` → `List<T>`

#### 2.3 Android特有API映射
- `SharedPreferences` → `shared_preferences` 插件
- `Context` → `BuildContext` 或移除（根据场景）
- `ConnectivityManager` → `connectivity_plus` 插件
- `Handler/Looper` → `Timer` 或 `Stream`
- `AsyncTask` → `Future` 和 `async/await`
- `Intent` → 路由导航或插件调用

### 3. 设计模式转换

#### 3.1 单例模式
```dart
// Android单例转换为Dart单例
class ClassName {
  static ClassName? _instance;
  
  ClassName._internal();
  
  static ClassName getInstance() {
    _instance ??= ClassName._internal();
    return _instance!;
  }
}
```

#### 3.2 回调接口
```dart
// Android接口转换为Dart typedef或抽象类
typedef CallbackFunction = void Function(String result);
// 或
abstract class CallbackInterface {
  void onSuccess(String result);
  void onError(String error);
}
```

### 4. 异步处理转换
- `AsyncTask` → `Future<T>` 和 `async/await`
- `Handler.post()` → `Future.delayed()` 或 `Timer`
- `Callback` → `Future<T>` 或 `Stream<T>`
- 线程池 → `Isolate` 或 `compute()`

### 5. 状态管理转换
根据复杂度选择合适的状态管理方案：
- **简单状态** → `StatefulWidget` + `setState()`
- **中等复杂度** → `Provider` 或 `Riverpod`
- **复杂状态** → `Bloc` 或 `GetX`

---

## 代码生成规范

### 1. 文件结构
```dart
// 文件头部注释
/// [功能描述]
/// 
/// 从Android代码转换而来：[原始类名]
/// 转换日期：[当前日期]

// 导入语句
import 'package:flutter/material.dart';
// TODO: 添加必要的依赖包导入

// 主要类定义
class DartClassName {
  // TODO: 根据Android代码实现具体逻辑
}
```

### 2. 注释规范
- **保留原有注释**：将Android代码中的注释转换为Dart注释
- **添加转换说明**：标明从哪个Android类/方法转换而来
- **标记TODO项**：对于不确定或需要进一步实现的部分
- **添加使用示例**：在类或方法注释中添加使用示例

### 3. TODO标记规范
### 4. 如果不确定的逻辑， 可以询问或者留下todo 注释
```dart
// TODO: [具体需要完成的任务]
// TODO-DEPENDENCY: 需要添加依赖包 [包名]
// TODO-PERMISSION: 需要添加权限 [权限名称]
// TODO-PLATFORM: 需要考虑平台差异 [具体说明]
// TODO-ASYNC: 需要实现异步逻辑 [具体说明]
// TODO-STATE: 需要集成状态管理 [建议方案]
```
### 5.  如果安卓代码里面存在一些不存在的变量, 可以在当前dart文件里面寻找是否有对应的变量， 不确定的可以询问或者留下todo 注释，


---

## 输出格式

### 1. 转换后的Dart代码
完整的Dart类或函数实现，包含：
- 正确的Dart语法
- 适当的类型声明
- 必要的导入语句
- 详细的注释说明

### 2. 依赖说明
列出需要添加的Flutter插件或包：
```yaml
dependencies:
  shared_preferences: ^2.0.0
  connectivity_plus: ^3.0.0
  # 其他依赖...
```

### 3. 使用说明
提供转换后代码的使用方法和注意事项

### 4. 待完成项清单
详细列出所有TODO项和需要进一步实现的功能

---

## 快速使用模板

### 基础模板
```
请根据ai_task/android_to_dart_task.md里面的任务描述执行任务

Android代码片段：
[完整的Android代码]

任务背景说明：
[功能描述、使用场景、业务逻辑等]
```

### 完整模板
```
请根据ai_task/android_to_dart_task.md里面的任务描述执行任务

Android代码片段：
[完整的Android代码]

任务背景说明：
[功能描述、使用场景、业务逻辑等]

目标Dart文件路径：
[可选：指定生成文件的路径]

依赖信息：
[可选：相关依赖和工具类信息]

特殊要求：
[可选：性能、兼容性等特殊要求]
```

## 执行流程总结
1. **分析Android代码** → 识别类结构、设计模式、API使用
2. **确定转换策略** → 选择合适的Dart实现方案
3. **生成Dart代码** → 应用转换规则，生成规范代码
4. **添加注释和TODO** → 标记不确定和待完成项
5. **输出完整方案** → 包含代码、依赖、使用说明
