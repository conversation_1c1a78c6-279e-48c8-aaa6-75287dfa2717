import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lib_base/config/theme_config.dart';
import 'package:lib_base/utils/business/image_util.dart';
import 'package:lib_base/config/route_utils.dart';
import 'package:lib_base/widgets/dialog/open_vip_dialog.dart';
import 'package:yyb_grammer_one_point_pass/model/http/grammar_column_response.dart';
import 'package:yyb_grammer_one_point_pass/config/router.dart';

class SubUnitItemView extends StatelessWidget {
  final GoryVoItem item;
  final VoidCallback? onTap;
  final VoidCallback? onReportTap;
  final VoidCallback? onSpecialTrainingTap; // 新增专项训练回调

  const SubUnitItemView({
    super.key,
    required this.item,
    this.onTap,
    this.onReportTap,
    this.onSpecialTrainingTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 12.h),
      padding: EdgeInsets.all(12.r),
      decoration: BoxDecoration(
        color: Color(0xFFF8F9FA), // 浅灰色背景
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 上半部分：图片 + 文本内容
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 左侧图片
              Container(
                width: 70.r,
                height: 70.r,
                child: Stack(
                  children: [
                    // 主图片
                    Container(
                      width: 70.r,
                      height: 70.r,
                      decoration: BoxDecoration(
                        color: ThemeConfig.currentTheme.colorWhiteGrey4,
                        borderRadius: BorderRadius.circular(6.r),
                      ),
                      child: item.imagePath?.isNotEmpty == true
                          ? ClipRRect(
                              borderRadius: BorderRadius.circular(6.r),
                              child: Image.network(
                                ImageUtil.getImageUrl(item.imagePath!),
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Icon(
                                    Icons.image,
                                    color: ThemeConfig
                                        .currentTheme.colorWhiteGrey2,
                                    size: 32.r,
                                  );
                                },
                              ),
                            )
                          : Icon(
                              Icons.image,
                              color: ThemeConfig.currentTheme.colorWhiteGrey2,
                              size: 32.r,
                            ),
                    ),
                    // 右下角标签
                    Positioned(
                      bottom: 2.r,
                      right: 2.r,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 4.w, vertical: 1.h),
                        decoration: BoxDecoration(
                          color: _isFree()
                              ? Color(0xFFE53E3E)
                              : Color(0xFFFF9800), // 免费红色，会员橙色
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        child: Text(
                          _isFree() ? "免费" : "会员",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 8.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(width: 12.w),
              // 右侧文本内容
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题
                    Text(
                      item.firstName ?? "",
                      style: ThemeConfig.currentTheme.text15.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 4.h),
                    // 描述
                    Text(
                      item.remarks ?? "",
                      style: ThemeConfig.currentTheme.text12P2.copyWith(
                        color: Colors.black54,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          // 下半部分：参与人数 + 按钮 (与图片左对齐)
          Row(
            children: [
              // 参与人数
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.person,
                    size: 12.r,
                    color: ThemeConfig.currentTheme.colorWhiteGrey2,
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    "${item.peopleNum ?? 0}人参与",
                    style: ThemeConfig.currentTheme.text12P2,
                  ),
                ],
              ),
              Spacer(), // 填充中间空间
              // 右侧按钮
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  InkWell(
                    onTap: () {
                      // 如果是会员内容，需要检查VIP权限
                      if (_isVipContent()) {
                        // 显示VIP弹框
                        OpenVipAlertDialog.showDialog(
                          moduleId: "grammarOnePointPass",
                          subject: "english",
                        );
                      } else {
                        // 免费内容，直接跳转
                        if (item.firstId?.isNotEmpty == true) {
                          toPage(
                            UnitLearningRouteName.grammarDetail,
                            extra: GrammarDetailPageParam(
                              firstId: item.firstId!,
                              title: item.firstName ?? "语法详情",
                            ),
                          );
                        }
                      }
                    },
                    child: Container(
                      height: 24.h, // 从32缩小到24
                      width: 60.w, // 固定宽度
                      decoration: BoxDecoration(
                        color: Color(0xFF4CAF50), // 绿色
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Center(
                        child: Text(
                          "专题精讲",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10.sp, // 从12缩小到10
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 8.w), // 从10缩小到8
                  InkWell(
                    onTap: () {
                      // 如果是会员内容，需要检查VIP权限
                      if (_isVipContent()) {
                        // 显示VIP弹框
                        OpenVipAlertDialog.showDialog(
                          moduleId: "grammarOnePointPass",
                          subject: "english",
                        );
                      } else {
                        // 免费内容，执行专项训练回调
                        onSpecialTrainingTap?.call();
                      }
                    },
                    child: Container(
                      height: 24.h, // 从32缩小到24
                      width: 60.w, // 固定宽度
                      decoration: BoxDecoration(
                        color: Color(0xFFFF9800), // 橙色
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Center(
                        child: Text(
                          "专项训练",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10.sp, // 从12缩小到10
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 判断是否为免费内容
  /// 根据item的isFree字段来判断
  bool _isFree() {
    // 如果isFree字段为"0"或"false"，则为免费内容，否则需要会员
    // 注意：这里的逻辑可能与字段名相反
    return item.isFree == "0" ||
        item.isFree?.toLowerCase() == "false" ||
        item.isFree == null;
  }

  /// 判断是否为会员内容
  bool _isVipContent() {
    return !_isFree();
  }
}
