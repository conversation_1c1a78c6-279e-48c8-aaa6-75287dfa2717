import 'dart:async';
import 'dart:io';
import 'package:path_provider/path_provider.dart';

class SpeechEvaluatingFileUtil {
  //配音根目录
  static const String _businessFolder = 'eyyb/readingPointsTheManuscript';

  //录音文件子目录
  static const String _audioRecordFolder = 'audioRecorder';
  //缓存路径， 单词，句子练习路径， temp， 可清理
  static const String _dubbingTempFolder = 'dubbingTemp';

  //配音操作的根目录
  static Future<String> businessFolderPath() async {
    var dir = await getApplicationCacheDirectory();
    String folderPath = '${dir.path}/$_businessFolder';
    var dic = Directory(folderPath);
    if (!dic.existsSync()) {
      dic.createSync(recursive: true);
    }
    return folderPath;
  }

  //录音文件保存路径
  static Future<String> audioRecordFolder() async {
    var dir = await getApplicationCacheDirectory();
    String folderPath = '${dir.path}/$_businessFolder/$_audioRecordFolder';
    var dic = Directory(folderPath);
    if (!dic.existsSync()) {
      dic.createSync(recursive: true);
    }
    return folderPath;
  }

  //录音文件路径
  static Future<String> audioRecordFile(String resourceId,
      {String pref = '.wav'}) async {
    String folderPath = await audioRecordFolder();
    return "$folderPath/${resourceId}$pref";
  }

  static clearAudioRecordFolder() async {
    String folderPath = await audioRecordFolder();
    Directory(folderPath).deleteSync(recursive: true);
  }

  //配音缓存文件夹， 用于临时存放文件
  static Future<String> dubbingTempFolderPath() async {
    var dir = await getApplicationCacheDirectory();
    String folderPath = '${dir.path}/$_businessFolder/$_dubbingTempFolder';
    var dic = Directory(folderPath);
    if (!dic.existsSync()) {
      dic.createSync(recursive: true);
    }
    return folderPath;
  }
}
