// @keepTs
// @ts-nocheck
import InAppWebViewFlutterPlugin from '../InAppWebViewFlutterPlugin';
import { Disposable } from '../types/Disposable';
import PullToRefreshChannelDelegate from './PullToRefreshChannelDelegate';
import PullToRefreshSettings from './PullToRefreshSettings';
import InAppWebView from '../webview/in_app_webview/InAppWebView';
export default class PullToRefreshLayout implements Disposable {
    channelDelegate: PullToRefreshChannelDelegate | null;
    settings: PullToRefreshSettings;
    enabled: boolean;
    webView: InAppWebView | null;
    constructor(w5: InAppWebViewFlutterPlugin | null, x5: Object | null, y5: PullToRefreshSettings);
    setEnabled(v5: boolean): void;
    isEnabled(): boolean;
    setRefreshing(u5: boolean): void;
    isRefreshing(): boolean;
    onRefresh(): void;
    addView(t5: InAppWebView): void;
    prepare(): void;
    dispose(): void;
}
