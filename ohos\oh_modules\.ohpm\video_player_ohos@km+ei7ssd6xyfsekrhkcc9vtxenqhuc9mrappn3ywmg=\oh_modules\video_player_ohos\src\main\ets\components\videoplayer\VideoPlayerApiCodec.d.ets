// @keepTs
// @ts-nocheck
import StandardMessageCodec from '@ohos/flutter_ohos/src/main/ets/plugin/common/StandardMessageCodec';
import { ByteBuffer } from '@ohos/flutter_ohos/src/main/ets/util/ByteBuffer';
export declare class VideoPlayerApiCodec extends StandardMessageCodec {
    static INSTANCE: VideoPlayerApiCodec;
    readValueOfType(m5: number, n5: ByteBuffer): ESObject;
    writeValue(k5: ByteBuffer, l5: ESObject): ESObject;
}
