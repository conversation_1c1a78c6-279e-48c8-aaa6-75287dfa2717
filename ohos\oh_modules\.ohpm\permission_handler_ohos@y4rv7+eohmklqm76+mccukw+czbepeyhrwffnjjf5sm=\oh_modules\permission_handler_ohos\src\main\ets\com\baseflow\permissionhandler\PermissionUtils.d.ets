// @keepTs
// @ts-nocheck
import ArrayList from '@ohos.util.ArrayList';
import { Callback } from '@ohos.base';
export default class PermissionUtils {
    static parseOhosName(a1: String): number;
    static hasPermissionInManifest(x: ArrayList<string>, y: string, z: Callback<boolean>): void;
    static getRequestPermission(v: ArrayList<string>, w: Callback<ArrayList<string>>): void;
    static getManifestNames(t: number, u: ESObject): void;
    static toPermissionStatus(s: number): number;
    static updatePermissionShouldShowStatus(r: number): void;
}
