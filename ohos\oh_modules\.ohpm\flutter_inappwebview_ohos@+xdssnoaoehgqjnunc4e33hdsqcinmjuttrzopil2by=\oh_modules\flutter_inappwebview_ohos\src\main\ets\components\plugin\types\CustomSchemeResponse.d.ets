// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
export default class CustomSchemeResponse {
    private data;
    private contentType;
    private contentEncoding;
    constructor(e24: ArrayBuffer, f24: string, g24: string);
    static fromMap(d24: Map<string, Any>): CustomSchemeResponse | null;
    getData(): ArrayBuffer;
    setData(c24: ArrayBuffer): void;
    getContentType(): string;
    setContentType(b24: string): void;
    getContentEncoding(): string;
    setContentEncoding(a24: string): void;
}
