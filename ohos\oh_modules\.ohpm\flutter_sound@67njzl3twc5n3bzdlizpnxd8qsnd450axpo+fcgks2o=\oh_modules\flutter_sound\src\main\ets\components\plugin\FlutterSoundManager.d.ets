// @keepTs
// @ts-nocheck
/**
 * Copyright (c) 2024 Hunan OpenValley Digital Industry Development Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { MethodCall, MethodChannel } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import { FlutterSoundSession } from './FlutterSoundSession';
export declare class FlutterSoundManager {
    channel: MethodChannel | null;
    slots: Map<number, FlutterSoundSession | undefined>;
    init(k2: MethodChannel): void;
    invokeMethod(i2: string, j2: Map<string, ESObject>): void;
    freeSlot(h2: number): void;
    getSession(g2: MethodCall): FlutterSoundSession | undefined;
    initSession(e2: MethodCall, f2: FlutterSoundSession): void;
    resetPlugin(c2: MethodCall, d2: MethodResult): void;
}
