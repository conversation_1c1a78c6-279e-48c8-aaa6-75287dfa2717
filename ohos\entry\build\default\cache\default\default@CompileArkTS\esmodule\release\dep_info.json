{"resolveConflictMode": true, "depName2RootPath": {"class-transformer": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\class-transformer@0.5.1\\oh_modules\\class-transformer", "audioplayers_ohos": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\audioplayers_ohos@xvb+z09rk3vd7mkhlegeaqh54x3iakcdjb42x9fzemi=\\oh_modules\\audioplayers_ohos", "flutter_inappwebview_ohos": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\flutter_inappwebview_ohos@+xdssnoaoehgqjnunc4e33hdsqcinmjuttrzopil2by=\\oh_modules\\flutter_inappwebview_ohos", "path_provider_ohos": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\path_provider_ohos@i52uuxzkgixcgipljxotoezo7ljmyme82ilwgmwgrki=\\oh_modules\\path_provider_ohos", "shared_preferences_ohos": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\shared_preferences_ohos@hpo+rpvqkbg4woljnvp1nngibolagne+ltekot8lzze=\\oh_modules\\shared_preferences_ohos", "url_launcher_ohos": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\url_launcher_ohos@trss0ed8wyr6ikj4a7wgbyvld2t0dqtpokc2gf29s5o=\\oh_modules\\url_launcher_ohos", "video_player_ohos": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\video_player_ohos@km+ei7ssd6xyfsekrhkcc9vtxenqhuc9mrappn3ywmg=\\oh_modules\\video_player_ohos", "permission_handler_ohos": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\permission_handler_ohos@y4rv7+eohmklqm76+mccukw+czbepeyhrwffnjjf5sm=\\oh_modules\\permission_handler_ohos", "package_info_plus": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\package_info_plus@w64mlphu6t+rsqhcb6fbyfuu+9jimq+bygowicngogm=\\oh_modules\\package_info_plus", "flutter_sound": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\flutter_sound@67njzl3twc5n3bzdlizpnxd8qsnd450axpo+fcgks2o=\\oh_modules\\flutter_sound", "connectivity_plus": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\connectivity_plus@qnu57+kbyujscglghdeenv0ewkxdzkwirnf79sjwyky=\\oh_modules\\connectivity_plus", "mobile_scanner": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\mobile_scanner@pzhl+kvhugccsao7zogbsd+32dz5iv+ocbij+pzhrxy=\\oh_modules\\mobile_scanner", "image_picker_ohos": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\image_picker_ohos@q+4qovzzk5oywfthtbna9+a8ns+uamve5ptw9i+rq14=\\oh_modules\\image_picker_ohos", "@pdp/book": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@pdp+book@kwetdlm8p3fehla1xud5h+ejryzid8z0alb9exoy9vg=\\oh_modules\\@pdp\\book", "singsound": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\singsound@3noxo1+ldk0a5+ax9utefojjt1ehalgj+carzztjwxy=\\oh_modules\\singsound", "device_info_plus": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\device_info_plus@k6znknfekuiajvxh8np8gwdzkpis9lb77htiologxai=\\oh_modules\\device_info_plus", "fluwx": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\fluwx@aset57cyvw9obg8gxx+wrtvzjel8cr+3piq9bclemhq=\\oh_modules\\fluwx", "flutter_image_compress_ohos": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\flutter_image_compress_ohos@ml7hvqg96wpjf8gdropkzbav7nbpwijc+njxlcqu6kq=\\oh_modules\\flutter_image_compress_ohos", "audio_session": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\audio_session@s5bt1r7kslgaujrhrebkvoq0c4mzijzr9kys8iy+j1g=\\oh_modules\\audio_session", "just_audio_ohos": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\just_audio_ohos@bri90m8bo2d4vwn+pokoxn+hgfkstvamdig3p2tb1sq=\\oh_modules\\just_audio_ohos", "screen_brightness_ohos": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\screen_brightness_ohos@cdi2fyy8p0hlvc2eroavd8qqfl5wqc7nhppjkkkdwpg=\\oh_modules\\screen_brightness_ohos", "@ohos/flutter_ohos": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@ohos+flutter_ohos@f9kqsf0oh0+eogbjodcyiku+2cnjmlxfqlbfyepzbuo=\\oh_modules\\@ohos\\flutter_ohos", "dayjs": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\dayjs@1.11.13\\oh_modules\\dayjs", "@ohos/lottie": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@ohos+lottie@2.0.23\\oh_modules\\@ohos\\lottie", "reflect-metadata": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\reflect-metadata@0.2.1\\oh_modules\\reflect-metadata", "@ohos/crypto-js": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@ohos+crypto-js@2.0.4\\oh_modules\\@ohos\\crypto-js", "@pdp/swiper": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@pdp+swiper@1.0.0\\oh_modules\\@pdp\\swiper", "@pdp/evaluation": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@pdp+evaluation@1.1.3\\oh_modules\\@pdp\\evaluation", "bigdata": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\bigdata@mptnw0wkm5spww48ifee6omoheeuqmqsa3pwhpz9qpk=\\oh_modules\\bigdata", "libsingsound.so": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\singsound@3noxo1+ldk0a5+ax9utefojjt1ehalgj+carzztjwxy=\\oh_modules\\singsound\\src\\main\\cpp\\types\\libsingsound", "@tencent/wechat_open_sdk": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@tencent+wechat_open_sdk@1.0.11\\oh_modules\\@tencent\\wechat_open_sdk", "@ohos/mp4parser": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@ohos+mp4parser@2.0.3-rc.1\\oh_modules\\@ohos\\mp4parser", "@umeng/common": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@umeng+common@1.1.3\\oh_modules\\@umeng\\common", "@umeng/analytics": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@umeng+analytics@1.2.4\\oh_modules\\@umeng\\analytics", "@ohos/hypium": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@ohos+hypium@1.0.6\\oh_modules\\@ohos\\hypium", "@types/libmp4parser_napi.so": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@ohos+mp4parser@2.0.3-rc.1\\oh_modules\\@ohos\\mp4parser\\src\\main\\cpp\\types\\libmp4parser_napi", "libcommon.so": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@umeng+common@1.1.3\\oh_modules\\@umeng\\common\\src\\main\\cpp\\types\\libcommon"}, "depName2DepInfo": {"class-transformer": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\class-transformer@0.5.1\\oh_modules\\class-transformer", "pkgName": "class-transformer", "pkgVersion": "0.5.1"}, "audioplayers_ohos": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\audioplayers_ohos@xvb+z09rk3vd7mkhlegeaqh54x3iakcdjb42x9fzemi=\\oh_modules\\audioplayers_ohos", "pkgName": "audioplayers_ohos", "pkgVersion": "1.0.0"}, "flutter_inappwebview_ohos": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\flutter_inappwebview_ohos@+xdssnoaoehgqjnunc4e33hdsqcinmjuttrzopil2by=\\oh_modules\\flutter_inappwebview_ohos", "pkgName": "flutter_inappwebview_ohos", "pkgVersion": "1.0.0"}, "path_provider_ohos": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\path_provider_ohos@i52uuxzkgixcgipljxotoezo7ljmyme82ilwgmwgrki=\\oh_modules\\path_provider_ohos", "pkgName": "path_provider_ohos", "pkgVersion": "1.0.0"}, "shared_preferences_ohos": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\shared_preferences_ohos@hpo+rpvqkbg4woljnvp1nngibolagne+ltekot8lzze=\\oh_modules\\shared_preferences_ohos", "pkgName": "shared_preferences_ohos", "pkgVersion": "1.0.0"}, "url_launcher_ohos": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\url_launcher_ohos@trss0ed8wyr6ikj4a7wgbyvld2t0dqtpokc2gf29s5o=\\oh_modules\\url_launcher_ohos", "pkgName": "url_launcher_ohos", "pkgVersion": "1.0.0"}, "video_player_ohos": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\video_player_ohos@km+ei7ssd6xyfsekrhkcc9vtxenqhuc9mrappn3ywmg=\\oh_modules\\video_player_ohos", "pkgName": "video_player_ohos", "pkgVersion": "1.0.0"}, "permission_handler_ohos": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\permission_handler_ohos@y4rv7+eohmklqm76+mccukw+czbepeyhrwffnjjf5sm=\\oh_modules\\permission_handler_ohos", "pkgName": "permission_handler_ohos", "pkgVersion": "1.0.0"}, "package_info_plus": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\package_info_plus@w64mlphu6t+rsqhcb6fbyfuu+9jimq+bygowicngogm=\\oh_modules\\package_info_plus", "pkgName": "package_info_plus", "pkgVersion": "1.0.0"}, "flutter_sound": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\flutter_sound@67njzl3twc5n3bzdlizpnxd8qsnd450axpo+fcgks2o=\\oh_modules\\flutter_sound", "pkgName": "flutter_sound", "pkgVersion": "1.0.0"}, "connectivity_plus": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\connectivity_plus@qnu57+kbyujscglghdeenv0ewkxdzkwirnf79sjwyky=\\oh_modules\\connectivity_plus", "pkgName": "connectivity_plus", "pkgVersion": "1.0.0"}, "mobile_scanner": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\mobile_scanner@pzhl+kvhugccsao7zogbsd+32dz5iv+ocbij+pzhrxy=\\oh_modules\\mobile_scanner", "pkgName": "mobile_scanner", "pkgVersion": "1.0.0"}, "image_picker_ohos": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\image_picker_ohos@q+4qovzzk5oywfthtbna9+a8ns+uamve5ptw9i+rq14=\\oh_modules\\image_picker_ohos", "pkgName": "image_picker_ohos", "pkgVersion": "1.0.0"}, "@pdp/book": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@pdp+book@kwetdlm8p3fehla1xud5h+ejryzid8z0alb9exoy9vg=\\oh_modules\\@pdp\\book", "pkgName": "@pdp/book", "pkgVersion": "1.0.0"}, "singsound": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\singsound@3noxo1+ldk0a5+ax9utefojjt1ehalgj+carzztjwxy=\\oh_modules\\singsound", "pkgName": "singsound", "pkgVersion": "1.0.0"}, "device_info_plus": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\device_info_plus@k6znknfekuiajvxh8np8gwdzkpis9lb77htiologxai=\\oh_modules\\device_info_plus", "pkgName": "device_info_plus", "pkgVersion": "1.0.0"}, "fluwx": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\fluwx@aset57cyvw9obg8gxx+wrtvzjel8cr+3piq9bclemhq=\\oh_modules\\fluwx", "pkgName": "fluwx", "pkgVersion": "1.0.0"}, "flutter_image_compress_ohos": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\flutter_image_compress_ohos@ml7hvqg96wpjf8gdropkzbav7nbpwijc+njxlcqu6kq=\\oh_modules\\flutter_image_compress_ohos", "pkgName": "flutter_image_compress_ohos", "pkgVersion": "1.0.0"}, "audio_session": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\audio_session@s5bt1r7kslgaujrhrebkvoq0c4mzijzr9kys8iy+j1g=\\oh_modules\\audio_session", "pkgName": "audio_session", "pkgVersion": "1.0.0"}, "just_audio_ohos": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\just_audio_ohos@bri90m8bo2d4vwn+pokoxn+hgfkstvamdig3p2tb1sq=\\oh_modules\\just_audio_ohos", "pkgName": "just_audio_ohos", "pkgVersion": "1.0.0"}, "screen_brightness_ohos": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\screen_brightness_ohos@cdi2fyy8p0hlvc2eroavd8qqfl5wqc7nhppjkkkdwpg=\\oh_modules\\screen_brightness_ohos", "pkgName": "screen_brightness_ohos", "pkgVersion": "1.0.0"}, "@ohos/flutter_ohos": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@ohos+flutter_ohos@f9kqsf0oh0+eogbjodcyiku+2cnjmlxfqlbfyepzbuo=\\oh_modules\\@ohos\\flutter_ohos", "pkgName": "@ohos/flutter_ohos", "pkgVersion": "1.0.0-299265ba05"}, "dayjs": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\dayjs@1.11.13\\oh_modules\\dayjs", "pkgName": "dayjs", "pkgVersion": "1.11.13"}, "@ohos/lottie": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@ohos+lottie@2.0.23\\oh_modules\\@ohos\\lottie", "pkgName": "@ohos/lottie", "pkgVersion": "2.0.23"}, "reflect-metadata": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\reflect-metadata@0.2.1\\oh_modules\\reflect-metadata", "pkgName": "reflect-metadata", "pkgVersion": "0.2.1"}, "@ohos/crypto-js": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@ohos+crypto-js@2.0.4\\oh_modules\\@ohos\\crypto-js", "pkgName": "@ohos/crypto-js", "pkgVersion": "2.0.4"}, "@pdp/swiper": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@pdp+swiper@1.0.0\\oh_modules\\@pdp\\swiper", "pkgName": "@pdp/swiper", "pkgVersion": "1.0.0"}, "@pdp/evaluation": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@pdp+evaluation@1.1.3\\oh_modules\\@pdp\\evaluation", "pkgName": "@pdp/evaluation", "pkgVersion": "1.1.3"}, "bigdata": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\bigdata@mptnw0wkm5spww48ifee6omoheeuqmqsa3pwhpz9qpk=\\oh_modules\\bigdata", "pkgName": "bigdata", "pkgVersion": "1.0.0"}, "libsingsound.so": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\singsound@3noxo1+ldk0a5+ax9utefojjt1ehalgj+carzztjwxy=\\oh_modules\\singsound\\src\\main\\cpp\\types\\libsingsound", "pkgName": "libsingsound.so", "pkgVersion": "1.0.0"}, "@tencent/wechat_open_sdk": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@tencent+wechat_open_sdk@1.0.11\\oh_modules\\@tencent\\wechat_open_sdk", "pkgName": "@tencent/wechat_open_sdk", "pkgVersion": "1.0.11"}, "@ohos/mp4parser": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@ohos+mp4parser@2.0.3-rc.1\\oh_modules\\@ohos\\mp4parser", "pkgName": "@ohos/mp4parser", "pkgVersion": "2.0.3-rc.1"}, "@umeng/common": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@umeng+common@1.1.3\\oh_modules\\@umeng\\common", "pkgName": "@umeng/common", "pkgVersion": "1.1.3"}, "@umeng/analytics": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@umeng+analytics@1.2.4\\oh_modules\\@umeng\\analytics", "pkgName": "@umeng/analytics", "pkgVersion": "1.2.4"}, "@ohos/hypium": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@ohos+hypium@1.0.6\\oh_modules\\@ohos\\hypium", "pkgName": "@ohos/hypium", "pkgVersion": "1.0.6"}, "@types/libmp4parser_napi.so": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@ohos+mp4parser@2.0.3-rc.1\\oh_modules\\@ohos\\mp4parser\\src\\main\\cpp\\types\\libmp4parser_napi", "pkgName": "libmp4parser_napi.so", "pkgVersion": ""}, "libcommon.so": {"pkgRootPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@umeng+common@1.1.3\\oh_modules\\@umeng\\common\\src\\main\\cpp\\types\\libcommon", "pkgName": "libcommon.so", "pkgVersion": "1.0.0"}}}