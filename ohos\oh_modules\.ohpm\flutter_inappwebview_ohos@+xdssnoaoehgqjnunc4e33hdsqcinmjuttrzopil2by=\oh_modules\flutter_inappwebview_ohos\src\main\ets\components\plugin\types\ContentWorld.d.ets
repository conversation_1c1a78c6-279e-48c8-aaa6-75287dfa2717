// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
export default class ContentWorld {
    private name;
    static PAGE: ContentWorld;
    static DEFAULT_CLIENT: ContentWorld;
    constructor(c11: string);
    static world(b11: string): ContentWorld;
    static fromMap(a11: Map<string, Any>): ContentWorld | null;
    getName(): string;
    setName(z10: string): void;
    equals(y10: Any): boolean;
}
