part of 'page_content_resolver.dart';

//下一个知识点
void _h5KnowledgeModule(InAppWebViewController webViewController,
    JsBridgeDataBean data, String content, WidgetRef ref) {
  String? knowledgeId = data.knowledgeId;
  String? moduleType = data.moduleType;
  String? unitId = data.unitId;
  String? bookId = data.bookId;
  if ("nextKnowledge" == moduleType) {
    //下一个知识点
    _handleNextKnowledge(webViewController, knowledgeId, bookId, unitId, ref);
  } else if ("weakKnowledge" == moduleType) {
    //我的薄弱知识点
  } else {
    //下一个错题知识点
  }
}

/// 处理下一个知识点逻辑
///
/// 从Android代码转换而来：mathPresenter.selectvideoresourcelist
/// 转换日期：2025-01-22
void _handleNextKnowledge(InAppWebViewController webViewController,
    String? knowledgeId, String? bookId, String? unitId, WidgetRef ref) {
  // 参数校验
  if (knowledgeId == null || bookId == null || unitId == null) {
    showToast("参数不完整！");
    return;
  }
  if (AppConfig.isNeedHideForTest) {
    showToast("敬请期待 !");
    return;
  }

  // 获取用户信息
  UserInfoModel userInfo = ref.read(userInfoNotifierProvider);

  // 调用接口获取视频资源列表
  BaseApiRepository.selectvideoresourcelist(
    bookId: bookId,
    oneUnitId: unitId,
    userId: userInfo.userId,
  ).then((response) {
    if (response.isSuccess && response.isDataNotNull) {
      MathVideoBean item = response.dataNotNull;

      // 查找下一个知识点
      InfoVoList? nextItem = _findNextKnowledgeItem(item, knowledgeId);

      if (nextItem == null) {
        showToast("未配置知识点视频！");
        return;
      }

      // 判断是否是最后一个
      String isLast = _isLastKnowledgeItem(item, knowledgeId) ? "1" : "0";

      //  跳转到数学视频播放页面
      // 原Android代码：startActivity(MathVideoPlayActivity.newIntent(WebActivity.this, bookId, "80", "1103", nextItem, isLast));
      // 需要根据实际的Flutter页面路由进行调整
      toPage(RouteName.mathVideoPlayPage,
          extra: MathVideoPlayPageParam(
            moduleId: "80",
            bookId: bookId,
            promoteGroup: "1103",
            infoVo: nextItem,
            isLast: isLast,
          )).then((v) {
        sendToJs(webViewController, methodName: "onResume");
      });
    } else {
      showToast("没有资源数据！");
    }
  });
}

/// 查找下一个知识点项目
InfoVoList? _findNextKnowledgeItem(MathVideoBean item, String knowledgeId) {
  // 将所有视频信息展平到一个列表中
  List<InfoVoList> videoInfos = [];
  if (item.resultVoList != null) {
    for (var resultItem in item.resultVoList!) {
      if (resultItem.infoVoList != null) {
        videoInfos.addAll(resultItem.infoVoList!);
      }
    }
  }

  // 查找当前知识点的下一个项目
  for (int i = 0; i < videoInfos.length; i++) {
    if (videoInfos[i].id == knowledgeId) {
      if (i + 1 < videoInfos.length) {
        return videoInfos[i + 1];
      }
      break;
    }
  }

  return null;
}

/// 判断是否是最后一个知识点
bool _isLastKnowledgeItem(MathVideoBean item, String knowledgeId) {
  // 将所有视频信息展平到一个列表中
  List<InfoVoList> videoInfos = [];
  if (item.resultVoList != null) {
    for (var resultItem in item.resultVoList!) {
      if (resultItem.infoVoList != null) {
        videoInfos.addAll(resultItem.infoVoList!);
      }
    }
  }

  // 查找当前知识点的位置
  for (int i = 0; i < videoInfos.length; i++) {
    if (videoInfos[i].id == knowledgeId) {
      // 如果下一个是最后一个，则返回true
      return (i + 1) == (videoInfos.length - 1);
    }
  }

  return false;
}
