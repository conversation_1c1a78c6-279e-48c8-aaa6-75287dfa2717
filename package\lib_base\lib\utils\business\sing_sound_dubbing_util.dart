import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:lib_base/log/log.dart';
import 'package:lib_base/model/sing_sound_result_info.dart';
import 'package:lib_base/model/user_info_model.dart';
import 'package:lib_base/resource/plugins/sing_sound_sdk_plugin/sing_sound_sdk_plugin.dart';
import 'package:lib_base/utils/permission_request_util.dart';
import 'package:lib_base/utils/ui_util.dart';
import 'package:lib_base/widgets/animation/image_switch_animation.dart';
import 'package:lib_base/widgets/business/record_play_action_row/record_button_animation_view.dart';
import 'package:lib_base/utils/ffmpeg_util.dart';
import 'package:flutter_sound_platform_interface/flutter_sound_platform_interface.dart'
    as sound;
import 'package:flutter_sound/public/flutter_sound_player.dart';

mixin ControlNotifierModelMixin {
  Map<String, ControlNotifierModel> controlMap = {};

  ControlNotifierModel getControlModel(String id, {bool hasRecord = true}) {
    ControlNotifierModel? model = controlMap[id];
    if (model == null) {
      if (hasRecord) {
        model = ControlNotifierModel.allValue(id);
      } else {
        model = ControlNotifierModel.noRecord(id);
      }

      controlMap[id] = model;
    }
    return model;
  }
}

class ControlNotifierModel {
  ValueNotifier<String?>? recordStateNotifier;
  ValueNotifier<String?>? originPlayStateNotifier;
  ValueNotifier<String?>? replayHasResultStateNotifier;
  GlobalKey<ImageSwitchAnimationState>? replayKey;
  GlobalKey<RecordButtonAnimationViewState>? recordKey;
  final String notifierFlag;

  ControlNotifierModel(
    this.notifierFlag, {
    this.recordStateNotifier,
    this.originPlayStateNotifier,
    this.replayKey,
    this.recordKey,
    this.replayHasResultStateNotifier,
  });

  factory ControlNotifierModel.allValue(
    String notifierFlag,
  ) {
    return ControlNotifierModel(notifierFlag,
        recordStateNotifier: ValueNotifier(null),
        replayHasResultStateNotifier: ValueNotifier(null),
        replayKey: GlobalKey<ImageSwitchAnimationState>(),
        originPlayStateNotifier: ValueNotifier(null),
        recordKey: GlobalKey<RecordButtonAnimationViewState>());
  }
  factory ControlNotifierModel.noRecord(
    String notifierFlag,
  ) {
    return ControlNotifierModel(notifierFlag,
        replayHasResultStateNotifier: ValueNotifier(null),
        replayKey: GlobalKey<ImageSwitchAnimationState>(),
        originPlayStateNotifier: ValueNotifier(null));
  }
}

class SingSoundDubbingController {
  //录音状态
  bool _isRecording = false;

  //原音播放状态
  bool isOriginPlaying = false;

  //录音状态
  bool isRecording = false;

  //回放状态
  bool isReplaying = false;

  late FlutterSoundPlayer _myPlayer;

  bool _mPlayerIsInited = false;

  Timer? _recordTimer;

  SingSoundDubbingController() {
    _myPlayer = FlutterSoundPlayer();
  }

  void dispose() {
    // Be careful : you must `close` the recorder session when you have finished with it.
    _myPlayer.closePlayer();
    _releaseRecordEngin();
  }

  Future _stopRecorder({bool needRelease = false}) async {
    if (_isRecording) {
      Logger.info("========start to  stopRecorder");
      await SingSoundSdkPlugin.stopRecord();
      if (needRelease) {
        await _releaseRecordEngin();
      }
      Logger.info("========  stopRecorder ");
    } else {
      Logger.info("======== 没在录音中 ");
    }
  }

  Future _releaseRecordEngin() async {
    await SingSoundSdkPlugin.releaseEngin();
  }

  Future pausePlay() {
    return _pausePlay();
  }

  bool get isPlaying => _myPlayer.isPlaying;

  Future<Duration?> play(String fromURI, {VoidCallback? whenFinished}) async {
    if (_isRecording) {
      await _stopRecorder();
    }
    if (_myPlayer.isPlaying) {
      await _pausePlay();
    }
    await _initPlayer();

    return await _myPlayer.startPlayer(
        fromURI: fromURI, whenFinished: whenFinished);
  }

  Future<Duration?> _playFromURI(String fromURI,
      {VoidCallback? whenFinished}) async {
    if (_isRecording) {
      await _stopRecorder();
    }
    if (_myPlayer.isPlaying) {
      await _pausePlay();
    }
    await _initPlayer();

    return await _myPlayer.startPlayer(
        fromURI: fromURI, whenFinished: whenFinished);
  }

  Future<Duration?> _playFromURIWithSeek(String fromURI,
      {VoidCallback? whenFinished,
      Duration? seekDuration,
      Duration? endDuration}) async {
    if (_isRecording) {
      await _stopRecorder();
    }
    if (_myPlayer.isPlaying) {
      await _pausePlay();
    }
    await _initPlayer();
    Logger.info("===========  fromURI:$fromURI");
    await _myPlayer.startPlayer(
        fromURI: fromURI, codec: sound.Codec.mp3, whenFinished: whenFinished);
    _myPlayer.setSubscriptionDuration(Duration(milliseconds: 100));
    if (endDuration != null) {
      _myPlayer.onProgress?.listen((e) {
        Duration position = e.position;
        Logger.info("position:$position, endDuration:$endDuration");
        if (position.compareTo(endDuration) > 0) {
          _pausePlay();
          whenFinished?.call();
        }
      });
    }

    if (seekDuration != null) {
      await _myPlayer.seekToPlayer(seekDuration);
    }
  }

  Future<void> _pausePlay() async {
    await _myPlayer.stopPlayer();
    _mPlayerIsInited = false;
  }

  Future _initPlayer() async {
    if (!_mPlayerIsInited) {
      await _myPlayer.openPlayer();
      _mPlayerIsInited = true;
    }
  }

  Future<SingsoundResult?> _doRecord(int seconds, String word,
      String recordResultFile, ControlNotifierModel controlNotifierModel,
      {required bool childMode, String? defaultCoreType}) async {
    if (_myPlayer.isPlaying) {
      await _pausePlay();
    }
    if (_isRecording) {
      showToast("请勿频繁操作");
      return null;
    }
    bool hasPermission =
        await PermissionRequestUtil.requestMicrophonePermission(() async {});
    if (hasPermission) {
      isRecording = true;
      controlNotifierModel.replayHasResultStateNotifier?.value = null;
      controlNotifierModel.recordStateNotifier?.value =
          controlNotifierModel.notifierFlag;
      controlNotifierModel.recordKey?.currentState?.start();
      _isRecording = true;
      String userId = UserInfoModel.loadDefault().obj?.uid ?? "";
      var result = await SingSoundSdkPlugin.startEnEvaluator(
        refText: word,
        type: childMode ? "0" : "1",
        timeout: seconds,
        userId: userId,
        defaultCoreType: defaultCoreType,
        audoStop: false,
      );
      Logger.info("===========  先声评测result：${jsonEncode(result)} ");
      SingsoundResult? scoreResult =
          await _handleResult(result, recordResultFile);
      Logger.info(
          "===========  先声评测scoreResult：${scoreResult?.accuracy} === ${scoreResult?.fluency?.toJson()} ");
      isRecording = false;
      controlNotifierModel.recordStateNotifier?.value = null;
      return scoreResult;
    }
  }

  Future<SingsoundResult?> _handleResult(
      result, String recordResultFile) async {
    if (result.toString() != 'false' && result is Map) {
      if (result['result'] != null) {
        SingSoundResultInfo info = SingSoundResultInfo.fromJson(result);

        //在此处理流畅度accuracy为null的情况
        if (info.result?.accuracy == null) {
          if (info.result?.details != null) {
            double r = 0;
            List<Details>? d = info.result?.details ?? [];
            for (int i = 0; i < d.length; i++) {
              r += d[i].score ?? 0;
            }
            info.result?.accuracy = r / d.length;
          }
        }

        if (info.requestId != null) {
          File? recordFile = await _getRecordFile();
          if (recordFile != null && recordFile.existsSync()) {
            //todo 转换录音文件
            File resultFile = File(recordResultFile);
            if (resultFile.existsSync()) {
              resultFile.deleteSync();
            }
            //插件录制的是pcm格式的， 需要转换为wav格式
            await FFMpegUtil.parsePcmToWav(recordFile.path, recordResultFile);
            await _releaseRecordEngin();
            return info.result;
          }
        }
      } else {
        if (result['errId'] != null) {}
      }
    }
    showToast("录音失败，请重试");
  }

  Future<File?> _getRecordFile() async {
    String path = await SingSoundSdkPlugin.getWavPath();
    Logger.info("path:$path");
    if (path.isNotEmpty) {
      return File(path);
    }
  }

  void playOrigin(String fromURI,
      {VoidCallback? whenComplete,
      required ControlNotifierModel controlNotifierModel}) async {
    await _pauseForPlayOrigin(controlNotifierModel);
    isOriginPlaying = true;
    controlNotifierModel.originPlayStateNotifier?.value =
        controlNotifierModel.notifierFlag;
    _playFromURI(fromURI, whenFinished: () {
      isOriginPlaying = false;
      controlNotifierModel.originPlayStateNotifier?.value = null;
      whenComplete?.call();
    });
  }

  Future pauseOrigin(ControlNotifierModel controlNotifierModel) async {
    controlNotifierModel.originPlayStateNotifier?.value = null;
    isOriginPlaying = false;
    await _pausePlay();
  }

  Future doPauseAll(ControlNotifierModel controlNotifierModel) async {
    pauseOrigin(controlNotifierModel);
    stopRecord(controlNotifierModel);
    stopReplay(controlNotifierModel);
  }

  // 停止录音和播放
  Future stopRecordAndOrigin() async {
    await _pausePlay();
    await _stopRecorder();
    _isRecording = false;
    _cancelRecordTimer();
  }

  Future _pauseForRecord(ControlNotifierModel controlNotifierModel) async {
    pauseOrigin(controlNotifierModel);
    stopReplay(controlNotifierModel);
  }

  Future _pauseForPlayOrigin(ControlNotifierModel controlNotifierModel) async {
    stopRecord(controlNotifierModel);
    stopReplay(controlNotifierModel);
  }

  Future _pauseForReplay(ControlNotifierModel controlNotifierModel) async {
    pauseOrigin(controlNotifierModel);
    stopRecord(controlNotifierModel);
  }

  Future<SingsoundResult?> startRecord(int seconds, String word,
      String recordResultFile, ControlNotifierModel controlNotifierModel,
      {bool childMode = true, String? defaultCoreType}) async {
    await _pauseForReplay(controlNotifierModel);
    if (seconds <= 0) {
      seconds = 1;
    }
    _recordTimer = Timer(Duration(seconds: seconds), () {
      stopRecord(controlNotifierModel);
    });
    return _doRecord(seconds, word, recordResultFile, controlNotifierModel,
        childMode: childMode, defaultCoreType: defaultCoreType);
  }

  void _cancelRecordTimer() {
    _recordTimer?.cancel();
    _recordTimer = null;
  }

  Future stopRecord(ControlNotifierModel controlNotifierModel) async {
    await _stopRecorder();
    controlNotifierModel.recordKey?.currentState?.stop();
    controlNotifierModel.recordStateNotifier?.value = null;
    _isRecording = false;
    _cancelRecordTimer();
  }

  Future stopReplay(ControlNotifierModel controlNotifierModel) async {
    isReplaying = false;
    controlNotifierModel.replayKey?.currentState?.stopAnimation();

    await _pausePlay();
  }

  Future startReplay(String filePath, ControlNotifierModel controlNotifierModel,
      {VoidCallback? whenComplete}) async {
    if (filePath.startsWith("http://") || filePath.startsWith("https://")) {
      //是网络请求, 啥也不干
    } else {
      File audioFile = File(filePath);
      if (!audioFile.existsSync()) {
        doPauseAll(controlNotifierModel);
        showToast("请先录音");
        return;
      }
    }

    await _pauseForRecord(controlNotifierModel);
    isReplaying = true;
    controlNotifierModel.replayKey?.currentState?.playAnimation();
    _playFromURI(filePath, whenFinished: () {
      stopReplay(controlNotifierModel);
      whenComplete?.call();
    });
  }
}
