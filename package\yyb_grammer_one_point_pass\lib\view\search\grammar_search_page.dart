import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lib_base/widgets/common/base_scaffold.dart';
import 'package:lib_base/config/route_utils.dart';
import 'package:lib_base/widgets/dialog/open_vip_dialog.dart';
import 'package:yyb_grammer_one_point_pass/model/http/grammar_column_response.dart';
import 'package:yyb_grammer_one_point_pass/model/http/grammar_search_response.dart';
import 'package:yyb_grammer_one_point_pass/view/home/<USER>/grammer_one_point_pass_controller.dart';
import 'package:yyb_grammer_one_point_pass/src/generated/assets.gen.dart';
import 'package:yyb_grammer_one_point_pass/config/router.dart';

class GrammarSearchPage extends ConsumerStatefulWidget {
  final GrammarColumnResponse? grammarResponse;

  const GrammarSearchPage({
    super.key,
    this.grammarResponse,
  });

  @override
  ConsumerState<GrammarSearchPage> createState() => _GrammarSearchPageState();
}

class _GrammarSearchPageState extends ConsumerState<GrammarSearchPage> {
  final TextEditingController _searchController = TextEditingController();
  List<GrammarSearchResult> _searchResults = [];
  bool _isSearching = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BaseScaffold(
      backgroundColor: Color(0xFF4CAF50), // 绿色背景，匹配截图
      appBar: AppBar(
        backgroundColor: Color(0xFF4CAF50),
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          "小升初语法一点通",
          style: TextStyle(
            color: Colors.white,
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          // 搜索框
          Container(
            margin: EdgeInsets.all(12.r),
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(25.r),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: "请输入内容",
                      hintStyle: TextStyle(
                        color: Colors.grey[400],
                        fontSize: 14.sp,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(vertical: 12.h),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? IconButton(
                              icon: Icon(
                                Icons.clear,
                                color: Colors.grey[400],
                                size: 18.r,
                              ),
                              onPressed: () {
                                _searchController.clear();
                                setState(() {
                                  _searchResults.clear();
                                });
                              },
                            )
                          : null,
                    ),
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.black87,
                    ),
                    onChanged: (value) {
                      setState(() {}); // 更新清空按钮显示状态
                    },
                    onSubmitted: (value) {
                      _performSearch(value);
                    },
                  ),
                ),
                InkWell(
                  onTap: () {
                    _performSearch(_searchController.text);
                  },
                  child: Container(
                    padding: EdgeInsets.all(8.r),
                    child: Icon(
                      Icons.search,
                      color: Colors.grey[400],
                      size: 20.r,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // 搜索结果或空状态
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Color(0xFFF5F5F5),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20.r),
                  topRight: Radius.circular(20.r),
                ),
              ),
              child: _buildSearchContent(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchContent() {
    if (_searchController.text.isEmpty) {
      // 空状态 - 显示可爱的插图和提示文字
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Center(
              child: Assets.images.grammarEmptySearchIcon
                  .image(width: 120.r, height: 120.r)),
          SizedBox(height: 14.h),
          Text(
            "请输入语法关键词进行搜索吧！",
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      );
    } else if (_isSearching) {
      // 搜索中状态
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
            ),
            SizedBox(height: 16.h),
            Text(
              "搜索中...",
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    } else if (_searchResults.isEmpty) {
      // 无搜索结果
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Center(
              child: Assets.images.grammarEmptySearchIcon
                  .image(width: 120.r, height: 120.r)),
          SizedBox(height: 14.h),
          Text(
            "没有找到相关内容",
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            "试试其他关键词吧",
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.grey[500],
            ),
          ),
        ],
      );
    } else {
      // 有搜索结果
      return ListView.builder(
        padding: EdgeInsets.all(16.r),
        itemCount: _searchResults.length,
        itemBuilder: (context, index) {
          final item = _searchResults[index];
          return _buildSearchResultItem(item);
        },
      );
    }
  }

  Widget _buildSearchResultItem(GrammarSearchResult item) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 2,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 左侧绿色圆点
          Container(
            width: 6.r,
            height: 6.r,
            margin: EdgeInsets.only(top: 6.h),
            decoration: BoxDecoration(
              color: Color(0xFF52C878), // 调整绿色颜色
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 8.w),
          // 免费/会员标签
          Container(
            padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
            decoration: BoxDecoration(
              color: _isItemFree(item)
                  ? Color(0xFFE53E3E)
                  : Color(0xFFFF9800), // 免费红色，会员橙色
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: Text(
              _isItemFree(item) ? "免费" : "会员",
              style: TextStyle(
                color: Colors.white,
                fontSize: 8.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          SizedBox(width: 8.w),
          // 中间文本内容
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 第一行：searchTitle（带关键词高亮）
                _buildHighlightedText(
                  item.searchTitle ?? "",
                  _searchController.text,
                  TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                  maxLines: 2,
                ),
                SizedBox(height: 14.h),
                // 第二行：remarks（带关键词高亮）
                if (item.remarks?.isNotEmpty == true) ...[
                  _buildHighlightedText(
                    item.remarks!,
                    _searchController.text,
                    TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.black54,
                    ),
                    maxLines: 2,
                  ),
                  SizedBox(height: 14.h),
                ],
                // 第三行：keyword（带关键词高亮）
                if (item.keyword?.isNotEmpty == true)
                  _buildHighlightedText(
                    item.keyword!,
                    _searchController.text,
                    TextStyle(
                      fontSize: 11.sp,
                      color: Colors.grey[600]!,
                    ),
                    maxLines: 3,
                  ),
              ],
            ),
          ),
          SizedBox(width: 8.w),
          // 右侧"去学习"按钮 - 与第一行标题对齐
          Container(
            margin: EdgeInsets.only(top: 2.h), // 微调对齐
            child: InkWell(
              onTap: () {
                _handleGoToLearn(item);
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: Color(0xFF52C878), // 调整按钮颜色
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Text(
                  "去学习",
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _performSearch(String query) async {
    if (query.isEmpty) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    try {
      // 调用控制器的搜索方法
      final controller =
          ref.read(grammerOnePointPassControllerProvider.notifier);
      final results = await controller.searchGrammarContent(query);
      setState(() {
        _searchResults = results;
        _isSearching = false;
      });
    } catch (e) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
      // 可以显示错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("搜索失败，请重试"),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 处理"去学习"按钮点击事件
  void _handleGoToLearn(GrammarSearchResult item) {
    // 如果是会员内容，显示VIP弹框
    if (!_isItemFree(item)) {
      OpenVipAlertDialog.showDialog(
        moduleId: "grammarOnePointPass",
        subject: "english",
      );
      return;
    }

    // 免费内容，直接跳转
    // 优先使用grammarCategoryFirstId，如果没有则使用id
    String? firstId = item.grammarCategoryFirstId?.isNotEmpty == true
        ? item.grammarCategoryFirstId
        : item.id;

    if (firstId?.isNotEmpty == true) {
      // 跳转到语法详情页面
      toPage(
        UnitLearningRouteName.grammarDetail,
        extra: GrammarDetailPageParam(
          firstId: firstId!,
          title: item.searchTitle ?? "",
          searchKeyword: _searchController.text,
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("无法跳转，缺少必要参数"),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 构建带关键词高亮的文本
  Widget _buildHighlightedText(
    String text,
    String keyword,
    TextStyle baseStyle, {
    int maxLines = 1,
  }) {
    if (keyword.isEmpty) {
      return Text(
        text,
        style: baseStyle,
        maxLines: maxLines,
        overflow: TextOverflow.ellipsis,
      );
    }

    List<TextSpan> spans = [];
    String lowerText = text.toLowerCase();
    String lowerKeyword = keyword.toLowerCase();

    int start = 0;
    int index = lowerText.indexOf(lowerKeyword);

    while (index != -1) {
      // 添加关键词前的文本
      if (index > start) {
        spans.add(TextSpan(
          text: text.substring(start, index),
          style: baseStyle,
        ));
      }

      // 添加高亮的关键词
      spans.add(TextSpan(
        text: text.substring(index, index + keyword.length),
        style: baseStyle.copyWith(
          color: Color(0xFF52C878), // 更新高亮绿色
          fontWeight: FontWeight.w600,
        ),
      ));

      start = index + keyword.length;
      index = lowerText.indexOf(lowerKeyword, start);
    }

    // 添加剩余的文本
    if (start < text.length) {
      spans.add(TextSpan(
        text: text.substring(start),
        style: baseStyle,
      ));
    }

    return RichText(
      text: TextSpan(children: spans),
      maxLines: maxLines,
      overflow: TextOverflow.ellipsis,
    );
  }

  /// 判断搜索结果项目是否为免费内容
  bool _isItemFree(GrammarSearchResult item) {
    // 根据与sub_unit_item_view相同的逻辑
    return item.isFree == "0" ||
        item.isFree?.toLowerCase() == "false" ||
        item.isFree == null;
  }
}
