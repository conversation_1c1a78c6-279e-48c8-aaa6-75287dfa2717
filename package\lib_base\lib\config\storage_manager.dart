import 'package:lib_base/config/extension/sp_extention.dart';
import 'package:localstorage/localstorage.dart';

class StorageManager {
  // / app全局配置 eg:theme
  static late CustomSharedPreferences sharedPreferences;

  static late LocalStorage homeworkStorage;

  static late LocalStorage webStorage;

  //课文背诵
  static late LocalStorage reciteTheTextStorage;

  //语文-课文点读
  static late LocalStorage readingPointsStorage;

  //普通的内容，可以缓存到这里面
  static late LocalStorage commonCacheStorage;

  /// 必备数据的初始化操作
  ///
  /// 由于是同步操作会导致阻塞,所以应尽量减少存储容量
  ///

  ///
  static Future initSp() async {
    await _initSp();
    _initLocalStorage();
  }

  static Future<CustomSharedPreferences> _initSp() async {
    // async 异步操作
    // sync 同步操作
    sharedPreferences = await CustomSharedPreferences.initSp();
    return sharedPreferences;
  }

  static _initLocalStorage() {
    homeworkStorage = LocalStorage("homeworkStorage");
    webStorage = LocalStorage("webStorage");
    reciteTheTextStorage = LocalStorage("reciteTheTextStorage");
    commonCacheStorage = LocalStorage("commonCacheStorage");
  }

  static void clearLocalStorage() {
    homeworkStorage.clear();
    webStorage.clear();
    reciteTheTextStorage.clear();
    commonCacheStorage.clear();
  }
}
