// @keepTs
// @ts-nocheck
import web_webview from '@ohos.web.webview';
import { Params } from '@ohos/flutter_ohos/src/main/ets/plugin/platform/PlatformView';
import URLRequest from '../../types/URLRequest';
import InAppWebViewFlutterPlugin from '../../InAppWebViewFlutterPlugin';
import InAppWebViewInterface from '../InAppWebViewInterface';
import InAppWebViewSettings from './InAppWebViewSettings';
import { Any } from '@ohos/flutter_ohos';
import UserScript from '../../types/UserScript';
import WebViewChannelDelegate from '../WebViewChannelDelegate';
import ContentWorld from '../../types/ContentWorld';
import { ValueCallback } from '../../types/ValueCallback';
import { WebMessageChannel } from '../web_message/WebMessageChannel';
import UserContentController from '../../types/UserContentController';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import InAppWebViewClient from './InAppWebViewClient';
import PluginScript from '../../types/PluginScript';
import { InAppBrowserDelegate } from '../../in_app_browser/InAppBrowserDelegate';
import HashMap from "@ohos.util.HashMap";
import PrintJobSettings from '../../print_job/PrintJobSettings';
import WebMessageListener from '../web_message/WebMessageListener';
import WebViewAssetLoaderExt from '../../types/WebViewAssetLoaderExt';
import ContentBlockerHandler from '../../content_blocker/ContentBlockerHandler';
import cert from '@ohos.security.cert';
import { FindInteractionController } from '../../find_interaction/FindInteractionController';
import PullToRefreshLayout from '../../pull_to_refresh/PullToRefreshLayout';
export default class InAppWebView implements InAppWebViewInterface {
    plugin: InAppWebViewFlutterPlugin;
    inAppBrowserDelegate: InAppBrowserDelegate | null;
    id: number;
    windowId: number | null | undefined;
    inAppWebViewClient: InAppWebViewClient | null;
    channelDelegate: WebViewChannelDelegate | null;
    private javaScriptBridgeInterface;
    customSettings: InAppWebViewSettings;
    isLoading: boolean;
    private inFullscreen;
    zoomScale: number;
    contentBlockerHandler: ContentBlockerHandler;
    regexToCancelSubFramesLoadingCompiled: RegExp | null;
    contextMenu: Map<string, Any> | undefined;
    initialPositionScrollStoppedTask: number;
    newCheckScrollStoppedTask: number;
    newCheckContextMenuShouldBeClosedTaskTask: number;
    userContentController: UserContentController;
    callAsyncJavaScriptCallbacks: Map<string, ValueCallback<string>>;
    evaluateJavaScriptContentWorldCallbacks: Map<string, ValueCallback<string>>;
    webMessageChannels: Map<string, WebMessageChannel>;
    webMessageListeners: Array<WebMessageListener>;
    private initialUserOnlyScripts;
    findInteractionController: FindInteractionController | null;
    webViewAssetLoaderExt: WebViewAssetLoaderExt | null;
    private interceptOnlyAsyncAjaxRequestsPluginScript;
    context: Context;
    controller: web_webview.WebviewController;
    private ohosWebViewModel;
    private controllerAttached;
    private pullToRefreshLayout;
    private viewWidth;
    private viewHeight;
    private curUrl;
    constructor(p4: Context, q4: InAppWebViewFlutterPlugin, r4: number, s4: number | null | undefined, t4: InAppWebViewSettings, u4: Array<UserScript>, v4?: Map<string, Any>);
    setPullToRefreshLayout(o4: PullToRefreshLayout): void;
    getPullToRefreshLayout(): PullToRefreshLayout;
    prepare(): Promise<void>;
    prepareAndAddUserScripts(): void;
    setIncognito(n4: boolean): void;
    setCacheEnabled(m4: boolean): void;
    loadUrl(l4: URLRequest): Promise<void>;
    loadFile(k4: string): Promise<void>;
    getLoadUrl(): string | Resource;
    getLoading(): boolean;
    clearCookies(): void;
    refresh(): void;
    scrollToTop(): void;
    clearAllCache(): void;
    takeScreenshot(i4: Map<string, Any> | null, j4: MethodResult): Promise<void>;
    setSettings(g4: InAppWebViewSettings, h4: Map<string, Any>): void;
    getCustomSettings(): Map<string, Any> | null;
    enablePluginScriptAtRuntime(d4: string, e4: boolean, f4: PluginScript): void;
    injectDeferredObject(z3: string, a4: ContentWorld | null, b4: string | null, c4: ValueCallback<string> | null): void;
    evaluateJavascript(w3: string, x3: ContentWorld | null, y3: ValueCallback<string> | null): void;
    injectJavascriptFileFromUrl(u3: string, v3: Map<string, Any> | null): void;
    injectCSSCode(t3: string): void;
    injectCSSFileFromUrl(r3: string, s3: Map<string, Any> | null): void;
    getCopyBackForwardList(): HashMap<string, Any>;
    scrollTo(o3: number, p3: number, q3: boolean): void;
    scrollBy(l3: number, m3: number, n3: boolean): Promise<void>;
    printCurrentPage(k3: PrintJobSettings): string | null;
    dispose(): void;
    getUrl(): string;
    getTitle(): string;
    getProgress(): number;
    reload(): void;
    canGoBack(): boolean;
    goBack(): void;
    canGoForward(): boolean;
    goForward(): void;
    canGoBackOrForward(j3: number): boolean;
    goBackOrForward(i3: number): void;
    stopLoading(): void;
    clearSslPreferences(): void;
    findAllAsync(h3: string): void;
    findNext(g3: boolean): void;
    clearMatches(): void;
    onPause(): void;
    onResume(): void;
    pauseTimers(): void;
    resumeTimers(): void;
    getContentHeight(): number;
    getContentWidth(f3: ValueCallback<number>): void;
    saveWebArchive(c3: string, d3: boolean, e3: ValueCallback<string>): void;
    isSecureContext(b3: ValueCallback<boolean>): void;
    zoomBy(a3: number): void;
    getOriginalUrl(): string;
    getZoomScale(): number;
    getHitTestResult(): web_webview.HitTestValue;
    pageDown(z2: boolean): boolean;
    pageUp(y2: boolean): boolean;
    zoomIn(): boolean;
    zoomOut(): boolean;
    clearFocus(): void;
    getCertificate(): Promise<cert.X509Cert>;
    clearHistory(): void;
    postUrl(w2: string, x2: ArrayBuffer | null): Promise<void>;
    loadDataWithBaseURL(r2: string, s2: string, t2: string, u2: string, v2: string): Promise<void>;
    getController(): web_webview.WebviewController;
    getInAppBrowserDelegate(): InAppBrowserDelegate | null;
    setContextMenu(q2: Map<string, Any>): void;
    requestFocusNodeHref(): Map<string, Any>;
    requestImageRef(): Map<string, Any>;
    createCompatWebMessageChannel(): WebMessageChannel;
    addWebMessageListener(p2: WebMessageListener): void;
    canScrollVertically(): boolean;
    canScrollHorizontally(o2: ValueCallback<boolean>): void;
    isInFullscreen(): boolean;
    setInFullscreen(n2: boolean): void;
    getPlugin(): InAppWebViewFlutterPlugin;
    getWebMessageChannels(): Map<string, WebMessageChannel>;
    getUserContentController(): UserContentController;
    onControllerAttached: () => void;
    onAreaChange: (oldValue: Area, newValue: Area) => void;
    toWebHeaders(m2: Map<string, string>): Array<web_webview.WebHeader>;
    private waitControllerAttached;
    private checkControllerAttached;
    isDebuggingEnabled(): boolean;
    disposeWebMessageChannels(): void;
    hideContextMenu(): void;
    getSelectedText(l2: ValueCallback<string>): void;
    callAsyncJavaScript(h2: string, i2: Map<string, Any>, j2: ContentWorld | null, k2: ValueCallback<string>): void;
    getView(): WrappedBuilder<[
        Params
    ]>;
    private onSettinsUpdated;
}
