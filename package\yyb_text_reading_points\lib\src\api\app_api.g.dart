// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_api.dart';

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers

class _AppApi implements AppApi {
  _AppApi(
    this._dio, {
    this.baseUrl,
  });

  final Dio _dio;

  String? baseUrl;

  @override
  Future<BaseResponse<List<ReadingPointsModel>>> listtextreadunit(
    String bookId,
    String terminalType,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'bookId': bookId,
      r'terminalType': terminalType,
    };
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<ReadingPointsModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/textbook/v50/textrecite/listtextreadunit',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<ReadingPointsModel>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<ReadingPointsModel>(
                  (i) => ReadingPointsModel.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<ReadingPointsDescribeModel>> explainPhone(
      String type) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{r'type': type};
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<ReadingPointsDescribeModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/appconfig-api/api/yyb/v1/explain/phone',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<ReadingPointsDescribeModel>.fromJson(
      _result.data!,
      (json) =>
          ReadingPointsDescribeModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<ExpandInfoModel>> getrecitesetting(
    String bookId,
    String userId,
    String terminalType,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'bookId': bookId,
      r'userId': userId,
      r'terminalType': terminalType,
    };
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<ExpandInfoModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/textbook-api/api/textbook/v50/resourcerecite/getrecitesetting',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<ExpandInfoModel>.fromJson(
      _result.data!,
      (json) => ExpandInfoModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<List<ClassHourInfoModel>>> resourcereciteGetrecite(
    String id,
    String bookId,
    String type,
    String userId,
    String terminalType,
    String phoneType,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'id': id,
      r'bookId': bookId,
      r'type': type,
      r'userId': userId,
      r'terminalType': terminalType,
      r'phoneType': phoneType,
    };
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<List<ClassHourInfoModel>>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/textbook-api/api/textbook/v50/resourcerecite/getrecite',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<List<ClassHourInfoModel>>.fromJson(
      _result.data!,
      (json) => json is List<dynamic>
          ? json
              .map<ClassHourInfoModel>(
                  (i) => ClassHourInfoModel.fromJson(i as Map<String, dynamic>))
              .toList()
          : List.empty(),
    );
    return value;
  }

  @override
  Future<BaseResponse<PointsInfoModel>> getreciteuserscore(
      String userId) async {
    const _extra = <String, dynamic>{
      'auto-loading': false,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{r'userId': userId};
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<PointsInfoModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/textbook-api/api/textbook/v50/resourcerecite/getreciteuserscore',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<PointsInfoModel>.fromJson(
      _result.data!,
      (json) => PointsInfoModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<ReviewModel>> getrecitereview(
      String moduleUnitConfigId) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'moduleUnitConfigId': moduleUnitConfigId
    };
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<ReviewModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/textbook-api/api/textbook/v50/resourcerecite/getrecitereview',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<ReviewModel>.fromJson(
      _result.data!,
      (json) => ReviewModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<PreviewModel>> getreciteprepare(
      String moduleUnitConfigId) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'moduleUnitConfigId': moduleUnitConfigId
    };
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<PreviewModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/textbook-api/api/textbook/v50/resourcerecite/getreciteprepare',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<PreviewModel>.fromJson(
      _result.data!,
      (json) => PreviewModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<ReadReciteModel>> getrecitepart(
    String moduleUnitConfigId,
    String userId,
    String moduleType,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{
      r'moduleUnitConfigId': moduleUnitConfigId,
      r'userId': userId,
      r'moduleType': moduleType,
    };
    final _headers = <String, dynamic>{};
    final Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<ReadReciteModel>>(Options(
      method: 'GET',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/textbook-api/api/textbook/v50/resourcerecite/getrecitepart',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<ReadReciteModel>.fromJson(
      _result.data!,
      (json) => ReadReciteModel.fromJson(json as Map<String, dynamic>),
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> savereciteuser(
    String userId,
    String moduleUnitConfigId,
    List<String> reciteIds,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = {
      'userId': userId,
      'moduleUnitConfigId': moduleUnitConfigId,
      'reciteIds': reciteIds,
    };
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api/textbook/v50/textrecite/savereciteuser',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  @override
  Future<BaseResponse<dynamic>> saveuserresult(
    String userId,
    String moduleUnitConfigId,
    String saveuserresult,
    String resultJson,
    String reciteType,
    String moduleResourceReciteId,
    String reciteState,
    int userScore,
    String unitName,
  ) async {
    const _extra = <String, dynamic>{
      'auto-loading': true,
      'without-login': false,
      'needErrorToast': true,
    };
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = {
      'userId': userId,
      'moduleUnitConfigId': moduleUnitConfigId,
      'saveuserresult': saveuserresult,
      'resultJson': resultJson,
      'reciteType': reciteType,
      'moduleResourceReciteId': moduleResourceReciteId,
      'reciteState': reciteState,
      'userScore': userScore,
      'unitName': unitName,
    };
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BaseResponse<dynamic>>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/textbook-api/api/textbook/v50/resourcerecite/saveuserresult',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(baseUrl: baseUrl ?? _dio.options.baseUrl)));
    final value = BaseResponse<dynamic>.fromJson(
      _result.data!,
      (json) => json as dynamic,
    );
    return value;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }
}
