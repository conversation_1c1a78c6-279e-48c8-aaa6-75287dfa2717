// @keepTs
// @ts-nocheck
import common from '@ohos.app.ability.common';
import camera from "@ohos.multimedia.camera";
/**
 * 如果获取对象失败，说明相机可能被占用或无法使用。如果被占用，须等到相机被释放后才能重新获取。
 * @param context
 * @returns
 */
export declare function getCameraManager(i: common.BaseContext): camera.CameraManager | undefined;
export declare function createSession(g: camera.CameraManager, h: camera.SceneMode): camera.PhotoSession | undefined;
export declare function hasFlashUnit(f: common.BaseContext): boolean;
