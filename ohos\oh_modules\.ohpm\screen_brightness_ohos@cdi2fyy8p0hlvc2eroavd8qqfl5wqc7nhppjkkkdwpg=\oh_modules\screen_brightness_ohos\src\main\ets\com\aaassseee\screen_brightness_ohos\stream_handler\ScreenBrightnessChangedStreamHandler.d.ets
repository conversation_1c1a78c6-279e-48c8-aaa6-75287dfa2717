// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
import { EventSink } from '@ohos/flutter_ohos/src/main/ets/plugin/common/EventChannel';
import { BaseStreamHandler } from './BaseStreamHandler';
type OnListenStartCallback = (eventSink: EventSink) => void;
export default class ScreenBrightnessChangedStreamHandler extends BaseStreamHandler {
    private onListenStart;
    constructor(j1: OnListenStartCallback | null);
    onListen(h1: Any, i1: EventSink): void;
    addScreenBrightnessToEventSink(g1: number): void;
}
export {};
