// @keepTs
// @ts-nocheck
declare class DateFormatUtil {
    /**
     * Seconds converted to HH:mm:ss.
     *
     * @param seconds Maximum video duration (seconds).
     * @return Time after conversion.
     */
    secondToTime(o2: number): string;
    /**
     * Zero padding, 2 bits.
     *
     * @param num Number to be converted.
     * @return Result after zero padding.
     */
    padding(l2: string): string;
}
declare const _default: DateFormatUtil;
export default _default;
