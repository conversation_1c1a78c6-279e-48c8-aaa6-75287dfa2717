// @keepTs
// @ts-nocheck
import URLProtectionSpace from '../types/URLProtectionSpace';
import CredentialDatabaseHelper from './CredentialDatabaseHelper';
export default class URLProtectionSpaceDao {
    credentialDatabaseHelper: CredentialDatabaseHelper;
    projection: Array<string>;
    constructor(k29: CredentialDatabaseHelper);
    getAll(): Promise<URLProtectionSpace[]>;
    find(g29: string, h29: string, i29: string, j29: number): Promise<URLProtectionSpace>;
    insert(f29: URLProtectionSpace): Promise<number>;
    delete(e29: URLProtectionSpace): Promise<number>;
}
