// @keepTs
// @ts-nocheck
import { Method<PERSON>all, MethodChannel } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import ChannelDelegateImpl from '../types/ChannelDelegateImpl';
import PullToRefreshLayout from './PullToRefreshLayout';
export default class PullToRefreshChannelDelegate extends ChannelDelegateImpl {
    private pullToRefreshView;
    constructor(d26: PullToRefreshLayout, e26: MethodChannel);
    onMethodCall(b26: MethodCall, c26: MethodResult): void;
    onRefresh(): void;
    dispose(): void;
}
