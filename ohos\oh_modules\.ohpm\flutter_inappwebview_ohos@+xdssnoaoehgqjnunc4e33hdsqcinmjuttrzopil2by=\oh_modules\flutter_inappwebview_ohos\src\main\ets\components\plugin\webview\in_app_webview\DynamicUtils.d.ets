// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
import { DVModelParameters } from '@ohos/flutter_ohos/src/main/ets/view/DynamicView/dynamicView';
export declare class DynamicUtils {
    static getParams(p6: DVModelParameters, q6: string): string | Any;
    static setParams(m6: DVModelParameters, n6: string, o6: Any): void;
}
export declare class DVModelJson {
    compType: string;
    children: Array<Any>;
    attributes: Any;
    events: Any;
    build: Any;
    constructor(h6: string, i6: Array<Any>, j6: Any, k6: Any, l6?: Any);
}
