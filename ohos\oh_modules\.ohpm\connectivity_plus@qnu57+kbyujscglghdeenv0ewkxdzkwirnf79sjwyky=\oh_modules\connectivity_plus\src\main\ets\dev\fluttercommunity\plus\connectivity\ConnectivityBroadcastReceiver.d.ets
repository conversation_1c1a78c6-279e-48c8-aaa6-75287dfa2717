// @keepTs
// @ts-nocheck
import { StreamHandler, EventSink } from '@ohos/flutter_ohos/src/main/ets/plugin/common/EventChannel';
import { Connectivity } from './Connectivity';
export declare class ConnectivityBroadcastReceiver implements StreamHandler {
    private connectivity;
    private context;
    private events;
    constructor(e: Context, f: Connectivity);
    onListen(c: Object, d: EventSink): void;
    onCancel(b?: Object): void;
    private sendEvent;
}
