import 'dart:async';
import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:lib_base/config/storage_manager.dart';
import 'package:lib_base/enums/oss_upload_item.dart';
import 'package:lib_base/log/log.dart';
import 'package:lib_base/model/sing_sound_result_info.dart';
import 'package:lib_base/providers/user/user_info_provider.dart';
import 'package:lib_base/utils/ali_oss_util.dart';
import 'package:lib_base/utils/business/image_util.dart';
import 'package:lib_base/utils/business/sing_sound_dubbing_util.dart';
import 'package:lib_base/widgets/dialog/simple_upload_progress_dialog.dart';
import 'package:lib_base/utils/ui_util.dart';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'package:yyb_text_reading_points/model/read_recite_model.dart';
import 'package:yyb_text_reading_points/src/api/api_repository.dart';
import 'package:yyb_text_reading_points/src/generated/assets.gen.dart';
import 'package:yyb_text_reading_points/util/speech_evaluating_file_util.dart';
import 'package:yyb_text_reading_points/view/pages/recite/operate_recite_page.dart';
import 'package:yyb_text_reading_points/view/pages/recite/widget/back_remind_popup.dart';
import 'package:yyb_text_reading_points/view/pages/recite/widget/count_down_view.dart';

part 'operate_recite_page_controller.g.dart';

@riverpod
class OperateRecitePageController extends _$OperateRecitePageController
    with ControlNotifierModelMixin {
  String get userId => ref.read(userInfoNotifierProvider).userId;

  ValueNotifier<bool> isRecording = ValueNotifier(false);

  SingSoundDubbingController singSoundDubbingController =
      SingSoundDubbingController();

  Timer? _timer;

  String currentPlayMp3File = '';

  late ControlNotifierModel _controlNotifierModel;

  late ReciteVo? _reciteVo;

  ValueNotifier<int> playMS = ValueNotifier(0);
  int imgIndex = 0;

  final List<AssetGenImage> imagePaths = [
    Assets.images.bottomBtnOriginalSoundPlay1,
    Assets.images.bottomBtnOriginalSoundPlay2,
    Assets.images.bottomBtnOriginalSoundPlay3,
  ];

  final List<AssetGenImage> replayImagePaths = [
    Assets.images.bottomBtnReplayIcon,
    Assets.images.bottomBtnReplayIcon1,
    Assets.images.bottomBtnReplayIcon2,
  ];

  ValueNotifier<SingsoundResult?> singSoundResult = ValueNotifier(null);

  late String _audioFilePath;

  ValueNotifier<double> currentProgress = ValueNotifier(0);

  int build() {
    ref.onDispose(() {
      _stopTimer();
      singSoundDubbingController.dispose();
    });
    return 0;
  }

  initController(OperateRecitePageParam param) async {
    _reciteVo = param.reciteVo;
    _controlNotifierModel = getControlModel(_reciteVo?.id ?? '');
    _audioFilePath =
        await SpeechEvaluatingFileUtil.audioRecordFile(_reciteVo?.id ?? '');
  }

  _startTimer() {
    _timer = Timer.periodic(Duration(milliseconds: 200), (timer) async {
      playMS.value += 1;
      if (playMS.value % 2 == 0) {
        if (singSoundDubbingController.isOriginPlaying) {
          imgIndex = (imgIndex + 1) % imagePaths.length;
        } else if (singSoundDubbingController.isReplaying) {
          imgIndex = (imgIndex + 1) % replayImagePaths.length;
        }
      }
      currentProgress.value += 0.2;
    });
  }

  _stopTimer() {
    _timer?.cancel();
    playMS.value = 0;
    currentProgress.value = 0;
  }

  backIntercept() {
    showSmartDialog(BackRemindPopup());
  }

  //范音
  playOriginalSound() {
    if (singSoundDubbingController.isOriginPlaying) {
      singSoundDubbingController.pauseOrigin(_controlNotifierModel);
      _stopTimer();
    } else {
      currentPlayMp3File = _reciteVo?.mp3Url ?? '';
      singSoundDubbingController.playOrigin(
        ImageUtil.getImageUrl(currentPlayMp3File),
        controlNotifierModel: _controlNotifierModel,
        whenComplete: () {
          _stopTimer();
        },
      );
      _startTimer();
    }
  }

  //回放
  playReplaySound() {
    String userMp3Url = _reciteVo?.userMp3Url ?? '';
    if (userMp3Url == '') {
      return;
    }
    if (singSoundDubbingController.isReplaying) {
      singSoundDubbingController.stopRecord(_controlNotifierModel);
      _stopTimer();
    } else {
      currentPlayMp3File = _reciteVo?.userMp3Url ?? '';
      singSoundDubbingController.startReplay(
        ImageUtil.getImageUrl(currentPlayMp3File),
        _controlNotifierModel,
        whenComplete: () {
          _stopTimer();
        },
      );
      _startTimer();
    }
  }

  //录音
  soundRecordingClick() {
    if (!singSoundDubbingController.isRecording) {
      isRecording.value = true;
      showSmartDialog(CountDownView(
        callBack: () {
          _startTimer();
          _doRecord();
        },
      ));
    } else {
      _doRecord();
      _stopTimer();
    }
  }

  //重录
  resetClick() {}

  //提交
  submitClick() {
    if (singSoundResult.value != null) {
      _uploadRecords(_audioFilePath, singSoundResult.value);
    }
  }

  _doRecord() async {
    if (!singSoundDubbingController.isRecording) {
      String content = _reciteVo?.content ?? '';
      content = content
          .replaceAll("<br>", "")
          .replaceAll("<br />", "")
          .replaceAll("[", "")
          .replaceAll("]", "")
          .replaceAll("<p class=\"ql-align-center\">", "")
          .replaceAll("<p style=\"text-align: left;\">", "")
          .replaceAll("<p style=\"text-align: center;\">", "")
          .replaceAll("<p style=\"text-align: right;\">", "")
          .replaceAll("\t", "")
          .replaceAll("<\\/p>", "")
          .replaceAll("<p>", "")
          .replaceAll("</p>", "");

      //开始录音
      singSoundResult.value = await singSoundDubbingController.startRecord(
        _reciteVo?.reciteTime ?? 0,
        content,
        _audioFilePath,
        _controlNotifierModel,
        defaultCoreType: _reciteVo?.type ?? 'cn.pred.score',
      );
      isRecording.value = false;
      Logger.error('${singSoundResult.value}');
      // if (!_isDispose) _uploadRecords(audioFilePath, singSoundResult);
    } else {
      //停止录音
      await singSoundDubbingController.stopRecord(_controlNotifierModel);
      isRecording.value = false;
    }
  }

  //停止录音
  void stopRecording() {
    _startTimer();
    _doRecord();
  }

  //上传记录
  _uploadRecords(String audioFilePath, SingsoundResult? singSoundResult) async {
    //上传音频信息
    SimpleUploadProgressDialog.showDialog(
      files: [audioFilePath],
      ossUploadItem: OssUploadItem.UPLOAD_RECORD,
      onComplete: (List<OssUploadResponse> result) async {
        dismissDialog();
        if (result[0].isSuccess) {
          String mp3File = result[0].objectKey ?? '';

          List<Details> dataList = singSoundResult?.details ?? [];
          num fluency = 0;
          num accuracy = 0;
          dataList.forEach((Details element) {
            fluency += (element.fluency?.overall ?? 0);
            accuracy += (element.score ?? 0);
            // element.s
          });
          num coherentScore =
              dataList.length != 0 ? fluency / dataList.length : fluency; //连贯分
          num completeScore = singSoundResult?.integrity ?? 0; //完整分
          num accuracyScore = dataList.length != 0
              ? accuracy / dataList.length
              : accuracy; //准确分
          num avgScore = singSoundResult?.overall ?? 0; //综合分

          //上传成功
          ApiRepository.saveuserresult(
            userId: userId,
            moduleUnitConfigId: _reciteVo?.moduleUnitConfigId ?? '',
            mp3Url: mp3File,
            resultJson: 'resultJson',
            reciteType: '1', //类型：0段落 1全文 2首
            moduleResourceReciteId: _reciteVo?.id ?? '',
            reciteState: '1', //背诵状态 1背 2读
            userScore: avgScore.toInt(),
            unitName: '',
          ).then((response) {
            if (response.isSuccess) {
              StorageManager.readingPointsStorage.setItem(
                  '${_reciteVo?.id}_$userId', jsonEncode(singSoundResult));
            }
          });
        } else {
          //失败了，重新上传
          _uploadRecords(audioFilePath, singSoundResult);
        }
      },
    );
  }
}
