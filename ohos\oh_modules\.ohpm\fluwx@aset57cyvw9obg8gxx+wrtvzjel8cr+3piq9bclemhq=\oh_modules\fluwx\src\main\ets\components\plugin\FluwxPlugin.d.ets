// @keepTs
// @ts-nocheck
import { AbilityAware, AbilityPluginBinding, FlutterPlugin, FlutterPluginBinding, MethodCall, MethodCallHandler, MethodResult, NewWantListener } from '@ohos/flutter_ohos';
import * as wechatSDK from "@tencent/wechat_open_sdk";
import AbilityConstant from "@ohos.app.ability.AbilityConstant";
import Want from "@ohos.app.ability.Want";
/** FluwxPlugin **/
export default class FluwxPlugin implements FlutterPlugin, MethodCallHandler, AbilityAware, NewWantListener, wechatSDK.WXApiEventHandler {
    private channel;
    private appContext;
    private uiContext;
    private binding;
    private authHandler;
    private shareHandler;
    private extMsg;
    getUniqueClassName(): string;
    onAttachedToEngine(l1: FlutterPluginBinding): void;
    onDetachedFromEngine(k1: FlutterPluginBinding): void;
    onAttachedToAbility(j1: AbilityPluginBinding): void;
    onDetachedFromAbility(): void;
    onMethodCall(h1: MethodCall, i1: MethodResult): void;
    onNewWant(f1: Want, g1: AbilityConstant.LaunchParam): void;
    onReq: (req: wechatSDK.BaseReq) => void;
    onResp: (resp: wechatSDK.BaseResp) => void;
    onAuthResponse(e1: wechatSDK.SendAuthResp): void;
    onSendMessageToWXResp(d1: wechatSDK.SendMessageToWXResp): void;
    onPayResp(c1: wechatSDK.PayResp): void;
    onLaunchMiniProgramResp(b1: wechatSDK.LaunchMiniProgramResp): void;
    handlePay(z: MethodCall, a1: MethodResult): Promise<void>;
    attemptToResumeMsgFromWx(y: MethodResult): void;
    launchMiniProgram(w: MethodCall, x: MethodResult): Promise<void>;
    openBusinessView(u: MethodCall, v: MethodResult): Promise<void>;
}
