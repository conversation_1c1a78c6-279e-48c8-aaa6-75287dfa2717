import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lib_base/config/route_utils.dart';
import 'package:lib_base/log/log.dart';

import '../../../../config/theme_config.dart';
import '../../../../resource/plugins/screen_plugin.dart';
import '../../controller/m_video_player_controller.dart';
import '../normal_video_player.dart';

class HorizontalVideo extends StatefulWidget {
  final String? videoUrl;
  final String? videoImageUrl;
  final bool isFile;
  final Duration? position;
  final bool autoPlay;
  final bool showFullScreenTap;
  const HorizontalVideo(
      {super.key,
      this.position,
      this.videoUrl,
      this.videoImageUrl,
      required this.isFile,
      this.autoPlay = true,
      this.showFullScreenTap = true});

  @override
  State<HorizontalVideo> createState() => _HorizontalVideoState();
}

class _HorizontalVideoState extends State<HorizontalVideo> {
  late MVideoPlayerController controller;

  @override
  void initState() {
    super.initState();
    controller = MVideoPlayerController();
  }

  Future _doBack() async {
    // await ScreenPlugin.switchToPortrait();
    controller.videoController?.pause();
    back(controller.value);
  }

  @override
  void dispose() {
    super.dispose();
    controller.videoController?.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Logger.info("==================videoUrl2:${widget.videoUrl}");
    return WillPopScope(
      onWillPop: () async {
        await _doBack();
        return false;
      },
      child: Stack(
        children: [
          NormalVideoPlayer(
            controller: controller,
            autoPlay: widget.autoPlay,
            videoUrl: widget.videoUrl,
            videoImageUrl: widget.videoImageUrl,
            isFile: widget.isFile,
            width: 1.sw,
            height: 1.sh,
            showSpeedControl: true,
            allowScrubbing:true,
            position: widget.position,
            onFullScreenTap: widget.showFullScreenTap
                ? (controller) async {
                    _doBack();
                  }
                : null,
            showFullScreenTap: widget.showFullScreenTap,
            moduleId: "",
            subject: "",
          ),
          Positioned(
            left: 10,
            top: MediaQuery.of(context).padding.top,
            child: InkWell(
              onTap: () {
                _doBack();
              },
              child: Container(
                padding: EdgeInsets.all(10.r),
                child: Icon(
                  Icons.arrow_back_ios,
                  color: ThemeConfig.currentTheme.colorWhite,
                  size: 22.sp,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
