// @keepTs
// @ts-nocheck
import ArrayList from '@ohos.util.ArrayList';
import StandardMessageCodec from '@ohos/flutter_ohos/src/main/ets/plugin/common/StandardMessageCodec';
import { ByteBuffer } from '@ohos/flutter_ohos/src/main/ets/util/ByteBuffer';
import MessageCodec from '@ohos/flutter_ohos/src/main/ets/plugin/common/MessageCodec';
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
export declare enum SourceCamera {
    REAR = 0,
    FRONT = 1
}
export declare enum SourceType {
    CAMERA = 0,
    GALLERY = 1
}
export declare enum CacheRetrievalType {
    IMAGE = 0,
    VIDEO = 1
}
export default class Messages {
    static wrapError(h2: Error): ArrayList<ESObject>;
}
export declare class FlutterError extends Error {
    code: string;
    details: ESObject;
    constructor(e2: string, f2: string, g2: ESObject);
}
export declare class GeneralOptions {
    private allowMultiple;
    private usePhotoPicker;
    private constructor();
    getAllowMultiple(): boolean;
    setAllowMultiple(d2: boolean): void;
    getUsePhotoPicker(): boolean;
    setUsePhotoPicker(c2: boolean): void;
    Builder: ESObject;
    toList(): ArrayList<ESObject>;
    static fromList(b2: ArrayList<ESObject>): GeneralOptions;
}
declare class ImageSelectionOptionsBuilder {
    setMaxWidth: (setterArg: number) => ESObject;
    setMaxHeight: (setterArg: number) => ESObject;
    setQuality: (setterArg: number) => ESObject;
    build: () => ESObject;
    constructor(x1: (setterArg: number) => ESObject, y1: (setterArg: number) => ESObject, z1: (setterArg: number) => ESObject, a2: () => ESObject);
}
export declare class ImageSelectionOptions {
    private maxWidth;
    private maxHeight;
    private quality;
    private constructor();
    getMaxWidth(): number;
    setMaxWidth(w1: number): void;
    getMaxHeight(): number;
    setMaxHeight(v1: number): void;
    getQuality(): number;
    setQuality(u1: number): void;
    Builder: ImageSelectionOptionsBuilder;
    toList(): ArrayList<ESObject>;
    static fromList(t1: ArrayList<ESObject>): ImageSelectionOptions;
}
declare class MediaSelectionOptionsBuilder {
    setImageSelectionOptions: (setterArg: ImageSelectionOptions) => ESObject;
    build: () => ESObject;
    constructor(r1: (setterArg: ImageSelectionOptions) => ESObject, s1: () => ESObject);
}
export declare class MediaSelectionOptions {
    private imageSelectionOptions;
    static imageSelectionOptions: ImageSelectionOptions | null;
    constructor();
    getImageSelectionOptions(): ImageSelectionOptions | null;
    setImageSelectionOptions(q1: ImageSelectionOptions | null): void;
    Builder: MediaSelectionOptionsBuilder;
    toList(): ArrayList<ESObject>;
    fromList(p1: ArrayList<ESObject>): MediaSelectionOptions;
}
declare class VideoSelectionOptionsBuilder {
    setMaxDurationSeconds: (setterArg: number) => ESObject;
    build: () => ESObject;
    constructor(n1: (setterArg: number) => ESObject, o1: () => ESObject);
}
export declare class VideoSelectionOptions {
    private maxDurationSeconds;
    static maxDurationSeconds: number;
    private constructor();
    getMaxDurationSeconds(): number;
    setMaxDurationSeconds(m1: number): void;
    Builder: VideoSelectionOptionsBuilder;
    toList(): ArrayList<ESObject>;
    static fromList(l1: ArrayList<ESObject>): VideoSelectionOptions;
}
declare class SourceSpecificationBuilder {
    setType: (setterArg: SourceType) => ESObject;
    setCamera: (setterArg: SourceCamera) => ESObject;
    build: () => ESObject;
    constructor(i1: (setterArg: SourceType) => ESObject, j1: (setterArg: SourceCamera) => ESObject, k1: () => ESObject);
}
export declare class SourceSpecification {
    private type;
    private camera;
    private constructor();
    getType(): SourceType;
    setType(h1: SourceType): void;
    getCamera(): SourceCamera;
    setCamera(g1: SourceCamera): void;
    Builder: SourceSpecificationBuilder;
    toList(): ArrayList<ESObject>;
    static fromList(f1: ArrayList<ESObject>): SourceSpecification;
}
export declare class CacheRetrievalErrorBuilder {
    setCode: null | ((setterArg: string) => ESObject);
    setMessage: ((setterArg: string) => ESObject) | null;
    build: (() => ESObject) | null;
    constructor(c1: null | ((setterArg: string) => ESObject), d1: ((setterArg: string) => ESObject) | null, e1: (() => ESObject) | null);
}
export declare class CacheRetrievalError {
    private code;
    private message;
    constructor();
    getCode(): string;
    setCode(b1: string): void;
    getMessage(): string;
    setMessage(a1: string): void;
    Builder: CacheRetrievalErrorBuilder;
    toList(): ArrayList<ESObject>;
    fromList(z: ArrayList<ESObject>): CacheRetrievalError;
}
export declare class CacheRetrievalResultBuilder {
    private type;
    private error;
    private paths;
    setType: ((setterArg: CacheRetrievalType) => ESObject) | null;
    setError: ((setterArg: CacheRetrievalError) => ESObject) | null;
    setPaths: ((setterArg: ArrayList<string>) => ESObject) | null;
    build: (() => ESObject) | null;
    constructor(v: ((setterArg: CacheRetrievalType) => ESObject) | null, w: ((setterArg: CacheRetrievalError) => ESObject) | null, x: ((setterArg: ArrayList<string>) => ESObject) | null, y: (() => ESObject) | null);
}
export declare class CacheRetrievalResult {
    private type;
    private error;
    private paths;
    constructor();
    getType(): CacheRetrievalType;
    setType(u: CacheRetrievalType): void;
    getError(): CacheRetrievalError | null;
    setError(t: CacheRetrievalError | null): void;
    getPaths(): ArrayList<string>;
    setPaths(s: ArrayList<string>): void;
    Builder: CacheRetrievalResultBuilder;
    toList(): ArrayList<ESObject>;
    fromList(r: ArrayList<ESObject>): CacheRetrievalResult;
}
export interface Result<T> {
    success(result: T): void;
    error(error: Error | ESObject): Error | ESObject;
}
export declare class ImagePickerApiCodec extends StandardMessageCodec {
    static INSTANCE: ImagePickerApiCodec;
    private constructor();
    readValueOfType(p: number, q: ByteBuffer): ESObject;
    writeValue(n: ByteBuffer, o: ESObject): void;
}
export declare abstract class ImagePickerApi {
    abstract pickImages(j: SourceSpecification, k: ImageSelectionOptions, l: GeneralOptions, m: Result<ArrayList<string>>): void;
    abstract pickVideos(f: SourceSpecification, g: VideoSelectionOptions, h: GeneralOptions, i: Result<ArrayList<string>>): void;
    abstract pickMedia(c: MediaSelectionOptions, d: GeneralOptions, e: Result<ArrayList<string>>): void;
    abstract retrieveLostResults(): Promise<CacheRetrievalResult>;
    static getCodec(): MessageCodec<ESObject>;
    static setup(a: BinaryMessenger | null, b?: ImagePickerApi | null): void;
}
export {};
