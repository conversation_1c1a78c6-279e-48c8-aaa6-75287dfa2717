// @keepTs
// @ts-nocheck
import List from "@ohos.util.List";
import { Any } from '@ohos/flutter_ohos';
export default class PermissionResponse {
    private resources;
    private action;
    constructor(z21: List<string>, a22: number);
    static fromMap(y21: Map<string, Any>): PermissionResponse | null;
    getResources(): List<string>;
    setResources(x21: List<string>): void;
    getAction(): number;
    setAction(w21: number): void;
    toString(): string;
}
