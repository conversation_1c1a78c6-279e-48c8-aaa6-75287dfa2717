// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'operate_recite_page_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$operateRecitePageControllerHash() =>
    r'cbc88d7f307ad6738a0945468ad747cda4da9759';

/// See also [OperateRecitePageController].
@ProviderFor(OperateRecitePageController)
final operateRecitePageControllerProvider =
    AutoDisposeNotifierProvider<OperateRecitePageController, int>.internal(
  OperateRecitePageController.new,
  name: r'operateRecitePageControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$operateRecitePageControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OperateRecitePageController = AutoDisposeNotifier<int>;
// ignore_for_file: unnecessary_raw_strings, subtype_of_sealed_class, invalid_use_of_internal_member, do_not_use_environment, prefer_const_constructors, public_member_api_docs, avoid_private_typedef_functions
