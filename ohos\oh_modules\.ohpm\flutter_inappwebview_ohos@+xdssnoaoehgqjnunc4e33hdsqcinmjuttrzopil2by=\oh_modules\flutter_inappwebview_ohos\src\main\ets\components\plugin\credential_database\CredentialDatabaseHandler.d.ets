// @keepTs
// @ts-nocheck
import { MethodCall } from '@ohos/flutter_ohos';
import InAppWebViewFlutterPlugin from '../InAppWebViewFlutterPlugin';
import ChannelDelegateImpl from '../types/ChannelDelegateImpl';
import CredentialDatabase from './CredentialDatabase';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
export default class CredentialDatabaseHandler extends ChannelDelegateImpl {
    credentialDatabase: CredentialDatabase | null;
    plugin: InAppWebViewFlutterPlugin | null;
    constructor(s30: InAppWebViewFlutterPlugin);
    init(r30: InAppWebViewFlutterPlugin): void;
    onMethodCall(p30: MethodCall, q30: MethodResult): void;
    private getAllAuthCredentials;
    private getHttpAuthCredentials;
    dispose(): void;
}
