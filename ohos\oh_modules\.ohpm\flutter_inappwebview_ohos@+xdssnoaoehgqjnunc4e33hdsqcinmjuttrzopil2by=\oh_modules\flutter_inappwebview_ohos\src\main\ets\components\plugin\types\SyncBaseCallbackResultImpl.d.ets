// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
import BaseCallbackResultImpl from './BaseCallbackResultImpl';
export default class SyncBaseCallbackResultImpl<T> extends BaseCallbackResultImpl<T> {
    result: T | null;
    responsed: boolean;
    defaultBehaviour(z23: T | null): void;
    success(y23: Any): void;
    waitResponse(): void;
    error(v23: string, w23: string, x23: Any): void;
    notImplemented(): void;
}
