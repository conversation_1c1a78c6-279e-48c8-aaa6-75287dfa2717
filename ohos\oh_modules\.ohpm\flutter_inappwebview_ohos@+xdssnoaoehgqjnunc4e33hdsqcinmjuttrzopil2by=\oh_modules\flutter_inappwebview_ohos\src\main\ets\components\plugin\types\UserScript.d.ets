// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
import ContentWorld from './ContentWorld';
import { UserScriptInjectionTime } from './UserScriptInjectionTime';
export default class UserScript {
    private groupName;
    private source;
    private injectionTime;
    private contentWorld;
    private allowedOriginRules;
    constructor(o5: string, p5: string, q5: UserScriptInjectionTime, r5: ContentWorld | null, s5: Set<string> | null);
    static fromMap(n5: Map<string, Any>): UserScript | null;
    getGroupName(): string;
    setGroupName(m5: string): void;
    getSource(): string;
    setSource(l5: string): void;
    getInjectionTime(): UserScriptInjectionTime;
    setInjectionTime(k5: UserScriptInjectionTime): void;
    getContentWorld(): ContentWorld;
    setContentWorld(j5: ContentWorld): void;
    getAllowedOriginRules(): Set<string>;
    getAllowedOriginRulesArray(): Array<string>;
    setAllowedOriginRules(i5: Set<string>): void;
    equals(h5: Any): boolean;
    private checkSetEquals;
}
