// @keepTs
// @ts-nocheck
/**
 * Copyright (c) 2024 Hunan OpenValley Digital Industry Development Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import media from '@ohos.multimedia.media';
import AudioContextOhos from '../AudioContextOhos';
import AudioplayersPlugin from '../AudioplayersPlugin';
import Source from '../source/Source';
import UrlSource from '../source/UrlSource';
import Player from './Player';
import WrappedPlayer from './WrappedPlayer';
import HashMap from "@ohos.util.HashMap";
export default class SoundPoolPlayer implements Player {
    wrappedPlayer: WrappedPlayer;
    private soundPoolManager;
    soundId: number | null;
    private streamId;
    private audioContext;
    private soundPoolWrapper;
    private soundPool;
    constructor(q: WrappedPlayer, r: SoundPoolManager);
    init(): Promise<void>;
    setAudioContext(p: AudioContextOhos): Promise<void>;
    getDuration(): number | null;
    getCurrentPosition(): number | null;
    isActuallyPlaying(): boolean;
    isLiveStream(): boolean;
    start(): Promise<void>;
    pause(): void;
    stop(): void;
    seekTo(o: number): void;
    release(): Promise<void>;
    setVolume(m: number, n: number): Promise<void>;
    setRate(l: number): Promise<void>;
    setLooping(k: boolean): Promise<void>;
    updateContext(j: AudioContextOhos): void;
    setSource(i: Source): void;
    prepare(): void;
    reset(): void;
    private unsupportedOperation;
    private convertRate;
    setUrlSource(h: UrlSource): Promise<void>;
    getUrlSource(): UrlSource | null;
}
export declare class SoundPoolManager {
    private ref;
    private legacySoundPoolWrapper;
    private soundPoolWrappers;
    constructor(g: AudioplayersPlugin);
    createSoundPoolWrapper(e: number, f: AudioContextOhos): Promise<void>;
    /**
     * Get the [SoundPoolWrapper] with the given [audioContext].
     */
    getSoundPoolWrapper(d: AudioContextOhos): SoundPoolWrapper | null;
    dispose(): void;
}
declare class SoundPoolWrapper {
    soundPool: media.SoundPool;
    soundIdToPlayer: HashMap<number, SoundPoolPlayer>;
    urlToPlayers: HashMap<UrlSource, SoundPoolPlayer[]>;
    constructor(c: media.SoundPool);
    /** For the onLoadComplete listener, track which sound id is associated with which player. An entry only exists until
     * it has been loaded.
     */
    /** This is to keep track of the players which share the same sound id, referenced by url. When a player release()s, it
     * is removed from the associated player list. The last player to be removed actually unloads() the sound id and then
     * the url is removed from this map.
     */
    dispose(): void;
}
export {};
