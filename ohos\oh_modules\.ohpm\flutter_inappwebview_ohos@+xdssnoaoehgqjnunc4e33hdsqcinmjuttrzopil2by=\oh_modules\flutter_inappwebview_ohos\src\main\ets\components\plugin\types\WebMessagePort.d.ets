// @keepTs
// @ts-nocheck
import { WebMessageChannel } from '../webview/web_message/WebMessageChannel';
import { ValueCallback } from './ValueCallback';
import WebMessage from './WebMessage';
export default class WebMessagePort {
    name: string;
    webMessageChannel: WebMessageChannel | null;
    isClosed: boolean;
    isTransferred: boolean;
    isStarted: boolean;
    constructor(z14: string, a15: WebMessageChannel | null);
    setWebMessageCallback(y14: ValueCallback<void>): void;
    postMessage(w14: WebMessage, x14: ValueCallback<void>): void;
    close(v14: ValueCallback<void>): void;
    dispose(): void;
}
