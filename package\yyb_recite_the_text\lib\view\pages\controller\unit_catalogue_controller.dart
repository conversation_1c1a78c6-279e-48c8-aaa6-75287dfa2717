import 'dart:async';
import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:lib_base/config/route_utils.dart';
import 'package:lib_base/config/storage_manager.dart';
import 'package:lib_base/model/sing_sound_result_info.dart';
import 'package:lib_base/providers/user/user_info_provider.dart';
import 'package:lib_base/utils/business/image_util.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:yyb_recite_the_text/config/router.dart';
import 'package:yyb_recite_the_text/model/unit_catalogue_model.dart';
import 'package:yyb_recite_the_text/src/api/api_repository.dart';
import 'package:yyb_recite_the_text/src/generated/assets.dart';
import 'package:yyb_recite_the_text/view/controller/recite_the_text_controller.dart';
import 'package:yyb_recite_the_text/view/pages/evaluation_results_page.dart';
import 'package:yyb_recite_the_text/view/pages/unit_catalogue_page.dart';

part 'unit_catalogue_controller.g.dart';

@riverpod
class UnitCatalogueController extends _$UnitCatalogueController {
  late UnitCataloguePageParam _param;
  ValueNotifier<List<UnitCatalogueModel>> currnetList = ValueNotifier([]);

  List<UnitCatalogueModel> allList = [];
  List<UnitCatalogueModel> dialogueList = [];
  List<UnitCatalogueModel> shortEssayList = [];

  int currentTabIndex = 0;

  String get userId => ref.read(userInfoNotifierProvider).userId;

  bool _mPlayerIsInited = false;

  ValueNotifier<double> currentValue = ValueNotifier(0); //当前播放进度

  ValueNotifier<bool> isPlay = ValueNotifier(false); //是否播放

  ValueNotifier<bool> isPaused = ValueNotifier(false); //是否暂停

  FlutterSoundPlayer player = FlutterSoundPlayer();

  String currentMp3File = '';

  Timer? _timer;

  ValueNotifier<int> playMS = ValueNotifier(0);

  final List<String> imagePaths = [
    Assets.replayYellowIcon,
    Assets.replayYellowIcon2,
    Assets.replayYellowIcon3,
    Assets.replayYellowIcon4,
  ];

  int currentPlayIndex = -1;

  build() {}

  initController(UnitCataloguePageParam param) {
    _param = param;
    loadData();
    _initPlayer();
  }

  void _startTimer() {
    _timer = Timer.periodic(Duration(milliseconds: 200), (timer) async {
      if (isPlay.value) {
        final position = await player.getProgress();
        Duration? progress = position['progress'];
        currentValue.value = progress?.inSeconds.toDouble() ?? 0;
      }
      playMS.value += 1;
    });
  }

  loadData({bool isRefresh = false}) {
    ApiRepository.querytextreciteinfo(
      userId: userId,
      moduleUnitConfigId: _param.moduleUnitConfigId,
    ).then((response) {
      if (response.isSuccess && response.isDataNotNull) {
        var res = response.dataNotNull;
        dialogueList = [];
        shortEssayList = [];
        allList = [];

        res.forEach((element) {
          if (element.type == '1') {
            //对话背诵
            dialogueList.add(element);
          } else if (element.type == '2') {
            //短文背诵
            shortEssayList.add(element);
          }
          allList.add(element);
          switchTabIndex(0);
        });
        if (_param.enterUnitPage && !isRefresh) toReciteTheManuscriptPage(0);
      }
    });
  }

  switchTabIndex(int index) {
    currentTabIndex = index;
    if (index == 0) {
      currnetList.value = allList;
    } else if (index == 1) {
      currnetList.value = dialogueList;
    } else if (index == 2) {
      currnetList.value = shortEssayList;
    }
  }

  //评分点击
  scoreClick(int index) {
    currentPlayIndex = index;
    stopAudio();
    Future.delayed(Duration(milliseconds: 100), () {
      UnitCatalogueModel item = currnetList.value[currentPlayIndex];
      SingsoundResult? singSoundResult;
      var result =
          StorageManager.reciteTheTextStorage.getItem('${item.id}_$userId');
      if (result != null) {
        singSoundResult = SingsoundResult.fromJson(jsonDecode(result));
      }
      toEvaluationResultsPage(
        EvaluationResultsPageParam(
            currentMp3File: item.mp3File ?? '',
            recordingMp3File: item.textReciteResultVo?.mp3File ?? '',
            result: singSoundResult,
            apiVoList: item.textReciteDetailApiVoList,
            isScoreEnter: true),
      );
    });
  }

  //原音按钮点击事件
  originalSoundBtnClick(int index) {
    UnitCatalogueModel item = currnetList.value[index];
    playEventClick(item.mp3File ?? '');
  }

  //背诵按钮点击事件
  reciteBtnClick(int index) {
    pauseAudio();
    toReciteTheManuscriptPage(index);
  }

  //回放按钮点击事件
  playbackBtnClick(int index) {
    UnitCatalogueModel item = currnetList.value[index];
    String mp3File = item.textReciteResultVo?.mp3File ?? '';
    if (mp3File == '') {
      toReciteTheManuscriptPage(index);
    } else {
      playEventClick(mp3File);
    }
  }

  //前往背诵页面
  toReciteTheManuscriptPage(int index) {
    currentPlayIndex = index;
    stopAudio();
    Future.delayed(Duration(milliseconds: 100), () {
      toPage(
        RouteName.reciteTheManuscriptPage,
        extra: currnetList.value[currentPlayIndex],
      );
    });
  }

  //播放下一段
  toNextParagraph(bool isScoreEnter) {
    if (currentPlayIndex != currnetList.value.length - 1) {
      currentPlayIndex += 1;
      toAndUtil(
        RouteName.reciteTheManuscriptPage,
        extra: currnetList.value[currentPlayIndex],
        backCount: isScoreEnter ? 1 : 2,
      );
    } else {
      //本单元最后一个了，需要跳转下一单元
      ReciteTheTextController controller =
          ref.read(reciteTheTextControllerProvider.notifier);
      controller.toNextDetailPage();
    }
  }

  playEventClick(String mp3File) {
    String playAudioUrl = ImageUtil.getImageUrl(mp3File);
    //如果当前播放的音频与点击的音频一致
    if (currentMp3File == mp3File) {
      if (isPlay.value) {
        pauseAudio();
      } else {
        playAudio(playAudioUrl);
      }
    } else {
      //不一致，先暂停播放，再播放新的音频
      stopAudio();
      playAudio(playAudioUrl, isReplay: true);
    }
    currentMp3File = mp3File;
  }

  Future _initPlayer() async {
    if (!_mPlayerIsInited) {
      await player.openPlayer();
      _mPlayerIsInited = true;
      _startTimer();
    }
  }

  Future<void> playAudio(String audiourl, {bool isReplay = false}) async {
    if (isPaused.value && !isReplay) {
      // 如果是暂停状态，恢复播放
      await player.resumePlayer();
      isPlay.value = true;
      isPaused.value = false;
    } else {
      // 从头开始播放
      await player.startPlayer(
        fromURI: audiourl,
        codec: Codec.mp3,
        whenFinished: () {
          // 播放完成回调
          stopAudio();
        },
      );
      isPlay.value = true;
      isPaused.value = false;
    }
  }

  Future<void> pauseAudio() async {
    if (isPlay.value) {
      await player.pausePlayer();
      isPlay.value = false;
      isPaused.value = true;
      playMS.value = 0;
    }
  }

  Future<void> stopAudio() async {
    if (isPlay.value) {
      await player.stopPlayer();
      isPlay.value = false;
      isPaused.value = false;
      playMS.value = 0;
    }
  }

  //前往评测结果页
  toEvaluationResultsPage(EvaluationResultsPageParam param) {
    toPage(RouteName.evaluationResultsPage, extra: param);
  }

  void dispose() {
    player.stopPlayer();
    player.closePlayer();
    _timer?.cancel();
    _timer = null;
  }
}
