// @keepTs
// @ts-nocheck
import { Method<PERSON>all, MethodChannel } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import ChannelDelegateImpl from '../types/ChannelDelegateImpl';
import { FindInteractionController } from './FindInteractionController';
export declare class FindInteractionChannelDelegate extends ChannelDelegateImpl {
    private findInteractionController;
    constructor(l30: FindInteractionController | null, m30: MethodChannel);
    onMethodCall(j30: MethodCall, k30: MethodResult): void;
    onFindResultReceived(g30: number, h30: number, i30: boolean): void;
    dispose(): void;
}
