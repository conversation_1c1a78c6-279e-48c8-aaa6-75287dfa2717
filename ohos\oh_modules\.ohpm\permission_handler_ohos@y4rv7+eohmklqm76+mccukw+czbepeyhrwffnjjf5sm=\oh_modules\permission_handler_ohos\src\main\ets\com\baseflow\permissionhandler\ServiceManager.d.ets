// @keepTs
// @ts-nocheck
import common from '@ohos.app.ability.common';
import { ErrorCallback } from './ErrorCallback';
export declare class ServiceManager {
    checkServiceStatus(n: number, o: common.Context, p: SuccessCallback, q: ErrorCallback): void;
    private isLocationServiceEnable;
    private isBluetoothServiceEnable;
}
export interface SuccessCallback {
    onSuccess(serviceStatus: number): void;
}
