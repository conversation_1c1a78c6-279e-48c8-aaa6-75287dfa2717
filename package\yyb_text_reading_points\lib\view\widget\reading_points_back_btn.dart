import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lib_base/config/route_utils.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:yyb_text_reading_points/src/generated/assets.gen.dart';

class ReadingPointsBackBtn extends ConsumerStatefulWidget {
  final VoidCallback? onBack;
  const ReadingPointsBackBtn({
    super.key,
    this.onBack,
  });

  @override
  ConsumerState<ReadingPointsBackBtn> createState() =>
      _ReadingPointsBackBtnState();
}

class _ReadingPointsBackBtnState extends ConsumerState<ReadingPointsBackBtn> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      child: Container(
        width: 35.r,
        height: 35.r,
        padding: EdgeInsets.symmetric(horizontal: 6.r, vertical: 3.r),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(30.r),
        ),
        child: Assets.images.chineseBackIcon.image(width: 20.r),
      ),
      onTap: () {
        widget.onBack == null ? back() : widget.onBack?.call();
      },
    );
  }
}
