import 'dart:async';
import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lib_base/enums/oss_upload_item.dart';
import 'package:lib_base/model/sing_sound_result_info.dart';
import 'package:lib_base/providers/user/user_info_provider.dart';
import 'package:lib_base/utils/ali_oss_util.dart';
import 'package:lib_base/src/utils/asset_utils.dart';
import 'package:lib_base/utils/business/sing_sound_dubbing_util.dart';
import 'package:lib_base/utils/ui_util.dart';
import 'package:lib_base/widgets/animation/image_switch_animation.dart';
import 'package:lib_base/widgets/dialog/simple_upload_progress_dialog.dart';
import 'package:lib_base/config/storage_manager.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:yyb_recite_the_text/model/unit_catalogue_model.dart';
import 'package:yyb_recite_the_text/src/api/api_repository.dart';
import 'package:yyb_recite_the_text/src/generated/assets.gen.dart';
import 'package:yyb_recite_the_text/src/utils/speech_evaluating_file_util.dart';
import 'package:yyb_recite_the_text/view/pages/controller/unit_catalogue_controller.dart';
import 'package:yyb_recite_the_text/view/pages/evaluation_results_page.dart';

part 'recite_the_manuscript_controller.g.dart';

@riverpod
class ReciteTheManuscriptController extends _$ReciteTheManuscriptController
    with ControlNotifierModelMixin {
  late UnitCatalogueController playController;
  String get userId => ref.read(userInfoNotifierProvider).userId;
  ValueNotifier<double> audioDuration = ValueNotifier(0); //音频总长度
  ValueNotifier<bool> isTranslation = ValueNotifier(false); //是否开启翻译

  ValueNotifier<bool> showReciteView = ValueNotifier(false); //是否显示背诵视图

  final List<AssetGenImage> imagePaths = [
    Assets.images.iconHorn1Yellow,
    Assets.images.iconHorn2Yellow,
    Assets.images.iconHorn3Yellow
  ];

  ValueNotifier<double> currentReciteValue = ValueNotifier(0); //当前录音进度
  ValueNotifier<int> reciteImgIndex = ValueNotifier(0);
  final List<AssetGenImage> reciteImagePaths = [
    Assets.images.recitAnimIcon1,
    Assets.images.recitAnimIcon2,
    Assets.images.reciteAnimIcon3,
    Assets.images.reciteAnimIcon4,
  ];

  Timer? _recordingTimer;

  List<String> _animationImages() {
    List<String> images = [];
    for (int i = 1; i <= 68; i++) {
      String imageName = "assets/images/easyword_anim_ready_$i.png";
      images.add(AssetsUtils.wrapAsset(imageName));
    }
    return images;
  }

  SingSoundDubbingController _singSoundDubbingController =
      SingSoundDubbingController();
  late ControlNotifierModel _controlNotifierModel;

  late UnitCatalogueModel _param;

  String _content = '';

  bool _isDispose = false;

  build() {}

  initController(UnitCatalogueModel param) {
    playController = ref.read(unitCatalogueControllerProvider.notifier);
    _param = param;
    audioDuration.value = (_param.audioTimes ?? 0) / 1000;
    _param.textReciteDetailApiVoList.forEach((element) {
      _content += ((element.content ?? '') + ' ');
    });
    playEventClick(_param.mp3File ?? '');
  }

  playEventClick(String mp3File) {
    playController.playEventClick(mp3File);
  }

  //改变播放进度
  void changePlayProgress(double value) {
    playController.player.seekToPlayer(Duration(seconds: value.toInt()));
  }

  void dispose() {
    _recordingTimer?.cancel();
    _isDispose = true;
    if (showReciteView.value) _doRecord();
    _singSoundDubbingController.dispose();
  }

  translateBtnClick() {
    isTranslation.value = !isTranslation.value;
  }

  reciteTheManuscriptClick(ControlNotifierModel controlNotifierModel) {
    _controlNotifierModel = controlNotifierModel;
    showSmartDialog(
      ImageSwitchAnimation(
        images: _animationImages(),
        width: 250.r,
        height: 250.r,
        loopCount: 1,
        autoPlay: true,
        duration: Duration(milliseconds: 50),
        onFinished: () {
          dismissDialog();
          _startRecording();
        },
      ),
      clickMaskDismiss: false,
      backDismiss: false,
    );
  }

  //开始录音
  void _startRecording() {
    playController.stopAudio();
    showReciteView.value = true;
    _doRecord();
    currentReciteValue.value = 0;
    _recordingTimer =
        Timer.periodic(Duration(milliseconds: 200), (timer) async {
      reciteImgIndex.value =
          (reciteImgIndex.value + 1) % reciteImagePaths.length;
      currentReciteValue.value += 0.2;
      if (currentReciteValue.value >= audioDuration.value) {
        stopRecording();
      }
    });
  }

  _doRecord() async {
    if (!_singSoundDubbingController.isRecording) {
      String audioFilePath =
          await SpeechEvaluatingFileUtil.audioRecordFile(_param.id ?? '');
      //开始录音
      SingsoundResult? singSoundResult =
          await _singSoundDubbingController.startRecord(
        audioDuration.value.toInt(),
        _content,
        audioFilePath,
        _controlNotifierModel,
      );
      if (!_isDispose) _uploadRecords(audioFilePath, singSoundResult);
    } else {
      //停止录音
      await _singSoundDubbingController.stopRecord(_controlNotifierModel);
    }
  }

  //停止录音
  void stopRecording() {
    _recordingTimer?.cancel();
    showReciteView.value = false;
    _doRecord();
  }

  //上传记录
  _uploadRecords(String audioFilePath, SingsoundResult? singSoundResult) async {
    //上传音频信息
    SimpleUploadProgressDialog.showDialog(
      files: [audioFilePath],
      ossUploadItem: OssUploadItem.UPLOAD_RECORD,
      onComplete: (List<OssUploadResponse> result) async {
        dismissDialog();
        if (result[0].isSuccess) {
          String mp3File = result[0].objectKey ?? '';

          List<Details> dataList = singSoundResult?.details ?? [];
          num fluency = 0;
          num accuracy = 0;
          dataList.forEach((Details element) {
            fluency += (element.fluency?.overall ?? 0);
            accuracy += (element.score ?? 0);
          });
          num coherentScore =
              dataList.length != 0 ? fluency / dataList.length : fluency; //连贯分
          num completeScore = singSoundResult?.integrity ?? 0; //完整分
          num accuracyScore = dataList.length != 0
              ? accuracy / dataList.length
              : accuracy; //准确分
          num avgScore = singSoundResult?.overall ?? 0; //综合分

          //上传成功
          ApiRepository.submitreciteresult(
            userId: userId,
            textReciteId: _param.id ?? '',
            mp3File: mp3File,
            coherentScore: coherentScore,
            completeScore: completeScore,
            accuracyScore: accuracyScore,
            avgScore: avgScore,
          ).then((response) {
            if (response.isSuccess) {
              StorageManager.reciteTheTextStorage
                  .setItem('${_param.id}_$userId', jsonEncode(singSoundResult));
              playController.loadData(isRefresh: true);
              playController.toEvaluationResultsPage(
                EvaluationResultsPageParam(
                  currentMp3File: playController.currentMp3File,
                  recordingMp3File: mp3File,
                  result: singSoundResult,
                  apiVoList: _param.textReciteDetailApiVoList,
                ),
              );
            }
          });
        } else {
          //失败了，重新上传
          _uploadRecords(audioFilePath, singSoundResult);
        }
      },
    );
  }
}
