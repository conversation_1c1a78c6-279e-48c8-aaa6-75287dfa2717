// @keepTs
// @ts-nocheck
import InAppWebView from '../webview/in_app_webview/InAppWebView';
import ContentWorld from './ContentWorld';
import { Disposable } from './Disposable';
import PluginScript from './PluginScript';
import UserScript from './UserScript';
import { UserScriptInjectionTime } from './UserScriptInjectionTime';
import HashSet from "@ohos.util.HashSet";
export default class UserContentController implements Disposable {
    protected static LOG_TAG: string;
    private contentWorlds;
    private inAppWebView;
    private userOnlyScripts;
    private pluginScripts;
    private isSupportedDocumentStartScript;
    private allStartScriptItem;
    private contentWorldsCreatorScript;
    private scriptHandlerMap;
    constructor(k12: InAppWebView);
    generateWrappedCodeForDocumentStart(): string;
    generateCodeForDocumentStart(): string;
    generatePluginScriptsCodeAt(j12: UserScriptInjectionTime): string;
    generateUserOnlyScriptsCodeAt(i12: UserScriptInjectionTime): string;
    getPluginScriptsAt(h12: UserScriptInjectionTime): HashSet<PluginScript>;
    getUserOnlyScriptsAt(g12: UserScriptInjectionTime): HashSet<UserScript>;
    wrapSourceCodeInContentWorld(e12: ContentWorld, f12: string): string;
    static escapeContentWorldName(d12: string): string;
    generateWrappedCodeForDocumentEnd(): string;
    static escapeCode(c12: string): string;
    private updateContentWorldsCreatorScript;
    generateContentWorldsCreatorCode(): string;
    addPluginScript(b12: PluginScript): boolean;
    addPluginScripts(a12: Array<PluginScript>): void;
    removePluginScript(z11: PluginScript): boolean;
    removeAllUserOnlyScripts(): void;
    removeAllPluginScripts(): void;
    addUserOnlyScript(y11: UserScript): boolean;
    getUserOnlyScriptAsList(): HashSet<UserScript>;
    getPluginScriptAsList(): HashSet<PluginScript>;
    removeUserOnlyScript(x11: UserScript): boolean;
    removeUserOnlyScriptAt(v11: number, w11: UserScriptInjectionTime): boolean;
    removeUserOnlyScriptsByGroupName(u11: string): void;
    addUserOnlyScripts(t11: Array<UserScript>): void;
    resetContentWorlds(): void;
    getPluginScriptsRequiredInAllContentWorlds(): HashSet<PluginScript>;
    generateCodeForScriptEvaluation(r11: string, s11: ContentWorld | null): string;
    private hasContentWorld;
    dispose(): void;
    private addDocumentStartJavaScript;
    getStartScripts(): ScriptItem[];
    private onDocumentStartChanged;
}
