// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
export default class Util {
    static LOG_TAG: string;
    constructor();
    static replaceAll(s12: string, t12: string, u12: string): string;
    static getOrDefault(p12: Map<string, Any>, q12: string, r12: Any): Any;
    static objEquals(n12: Any, o12: Any): boolean;
    static formatString(l12: string, ...m12: Any[]): string;
    static getPixelDensity(): number;
}
