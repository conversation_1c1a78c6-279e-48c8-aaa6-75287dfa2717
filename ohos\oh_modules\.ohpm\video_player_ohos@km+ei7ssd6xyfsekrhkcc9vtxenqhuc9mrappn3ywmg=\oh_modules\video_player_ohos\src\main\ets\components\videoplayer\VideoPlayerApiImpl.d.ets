// @keepTs
// @ts-nocheck
import { AbilityPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/ability/AbilityPluginBinding';
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import { CreateMessage, LoopingMessage, MixWithOthersMessage, PlaybackSpeedMessage, PositionMessage, TextureMessage, VolumeMessage } from './Messages';
import { FlutterState } from './VideoPlayerPlugin';
import common from '@ohos.app.ability.common';
export declare class VideoPlayerApiImpl {
    private videoPlayers;
    private flutterState;
    private binding;
    private AudioFocus;
    private pixelMaps;
    constructor(a11: FlutterState | null, b11: AbilityPluginBinding | null);
    private disposeAllPlayers;
    initialize(): void;
    detach(): void;
    getContext(): common.UIAbilityContext;
    create(b10: CreateMessage): Promise<TextureMessage>;
    dispose(z9: TextureMessage): void;
    setLooping(x9: LoopingMessage): void;
    setVolume(v9: VolumeMessage): void;
    setPlaybackSpeed(s9: PlaybackSpeedMessage): void;
    play(q9: TextureMessage): void;
    position(n9: TextureMessage): PositionMessage;
    seekTo(l9: PositionMessage): void;
    pause(j9: TextureMessage): void;
    setMixWithOthers(i9: MixWithOthersMessage): void;
    setup(w5: BinaryMessenger): void;
}
