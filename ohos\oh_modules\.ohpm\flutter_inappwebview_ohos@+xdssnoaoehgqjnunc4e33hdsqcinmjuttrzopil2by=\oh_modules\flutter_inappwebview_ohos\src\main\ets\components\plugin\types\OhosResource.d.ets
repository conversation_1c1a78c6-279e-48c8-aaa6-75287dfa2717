// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
export default class AndroidResource {
    private name;
    private defType;
    private defPackage;
    constructor(l26: string, m26: string | null, n26: string | null);
    static fromMap(k26: Map<string, Any>): AndroidResource | null;
    toMap(): Map<string, Any>;
    getName(): string;
    setName(j26: string): void;
    getDefType(): String;
    setDefType(i26: null): void;
    getDefPackage(): string;
    setDefPackage(h26: string): void;
    getIdentifier(g26: Context): number;
    equals(f26: Any): boolean;
    hashCode(): number;
    toString(): string;
}
