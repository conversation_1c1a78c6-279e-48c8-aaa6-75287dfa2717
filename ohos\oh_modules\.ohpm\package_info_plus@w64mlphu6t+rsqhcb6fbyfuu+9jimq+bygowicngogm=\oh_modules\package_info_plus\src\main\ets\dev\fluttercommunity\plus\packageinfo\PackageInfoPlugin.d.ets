// @keepTs
// @ts-nocheck
import { FlutterPlugin, FlutterPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import { <PERSON><PERSON><PERSON><PERSON>and<PERSON>, MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import MethodCall from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodCall';
export declare class PackageInfoPlugin implements FlutterPlugin, MethodCallHandler {
    getUniqueClassName(): string;
    private methodChannel;
    private applicationContext;
    onAttachedToEngine(j: FlutterPluginBinding): void;
    onDetachedFromEngine(i: FlutterPluginBinding): void;
    onMethodCall(a: MethodCall, b: MethodResult): void;
}
