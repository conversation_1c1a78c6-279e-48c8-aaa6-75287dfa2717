// @keepTs
// @ts-nocheck
/**
 * Copyright (c) 2024 Hunan OpenValley Digital Industry Development Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { FlutterPlugin, FlutterPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import { EventSink, StreamHandler } from '@ohos/flutter_ohos/src/main/ets/plugin/common/EventChannel';
import { AbilityAware, AbilityPluginBinding, EventChannel } from '@ohos/flutter_ohos';
import WrappedPlayer from './player/WrappedPlayer';
import audio from '@ohos.multimedia.audio';
export default class AudioplayersPlugin implements FlutterPlugin, AbilityAware, IUpdateCallback {
    private methods?;
    private globalMethods?;
    private globalEvents?;
    private context?;
    private binaryMessenger?;
    private soundPoolManager?;
    private players;
    private defaultAudioContext;
    private updateInterval;
    private abilityBinding?;
    private session?;
    private isStartContinuousTask;
    getUniqueClassName(): string;
    onAttachedToEngine(u2: FlutterPluginBinding): void;
    onDetachedFromEngine(t2: FlutterPluginBinding): void;
    private globalMethodCall;
    private methodCall;
    private getPlayer;
    getApplicationContext(): Context;
    getAudioManager(): audio.AudioManager;
    handleIsPlaying(): void;
    handleDuration(s2: WrappedPlayer): void;
    handleComplete(r2: WrappedPlayer): void;
    handlePrepared(p2: WrappedPlayer, q2: boolean): void;
    handleLog(n2: WrappedPlayer, o2: string): void;
    handleGlobalLog(m2: string): void;
    handleError(i2: WrappedPlayer, j2?: string, k2?: string, l2?: ESObject): void;
    handleGlobalError(f2?: string, g2?: string, h2?: ESObject): void;
    handleSeekComplete(e2: WrappedPlayer): void;
    startUpdates(): void;
    stopUpdates(): void;
    private updateCallback;
    private getAudioContext;
    private getObjectValue;
    private error;
    onAttachedToAbility(d2: AbilityPluginBinding): void;
    onDetachedFromAbility(): void;
    startContinuousTask(): Promise<void>;
    stopContinuousTask(): Promise<void>;
}
export declare class EventHandler implements StreamHandler {
    private eventChannel?;
    private eventSink;
    constructor(c2: EventChannel);
    onListen(a2: ESObject, b2: EventSink): void;
    onCancel(z1: ESObject): void;
    success(x1: string, y1?: Map<string, ESObject>): void;
    error(u1?: string, v1?: string, w1?: ESObject): void;
    dispose(): void;
}
interface IUpdateCallback {
    stopUpdates(): void;
    startUpdates(): void;
}
export {};
