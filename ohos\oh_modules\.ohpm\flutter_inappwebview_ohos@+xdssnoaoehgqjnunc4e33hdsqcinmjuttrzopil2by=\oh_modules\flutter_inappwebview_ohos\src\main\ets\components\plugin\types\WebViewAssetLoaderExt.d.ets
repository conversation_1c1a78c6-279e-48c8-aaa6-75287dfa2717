// @keepTs
// @ts-nocheck
import { Any, MethodChannel } from '@ohos/flutter_ohos';
import InAppWebViewFlutterPlugin from '../InAppWebViewFlutterPlugin';
import BaseCallbackResultImpl from './BaseCallbackResultImpl';
import ChannelDelegateImpl from './ChannelDelegateImpl';
import { Disposable } from './Disposable';
import WebResourceResponseExt from './WebResourceResponseExt';
import WebViewAssetLoader, { PathHandler } from './WebViewAssetLoader';
export default class WebViewAssetLoaderExt implements Disposable {
    loader: WebViewAssetLoader;
    customPathHandlers: Array<PathHandlerExt>;
    constructor(e14: WebViewAssetLoader, f14: Array<PathHandlerExt>);
    static fromMap(b14: Map<string, Any> | null, c14: InAppWebViewFlutterPlugin, d14: Context): WebViewAssetLoaderExt | null;
    dispose(): void;
}
declare class PathHandlerExt implements PathHandler, Disposable {
    protected staticLOG_TAG: string;
    static METHOD_CHANNEL_NAME_PREFIX: string;
    id: string;
    channelDelegate: PathHandlerExtChannelDelegate | null;
    constructor(z13: string, a14: InAppWebViewFlutterPlugin);
    handle(y13: string): WebResourceResponse;
    dispose(): void;
}
declare class PathHandlerExtChannelDelegate extends ChannelDelegateImpl {
    private pathHandler;
    constructor(w13: PathHandlerExt, x13: MethodChannel);
    handle(u13: string, v13: HandleCallback): void;
    handleSync(t13: string): Promise<WebResourceResponseExt>;
    dispose(): void;
}
declare class HandleCallback extends BaseCallbackResultImpl<WebResourceResponseExt> {
    decodeResult(s13: Any): WebResourceResponseExt | null;
}
export {};
