{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode", "INCREMENTAL_TASKS": {"COMPILE_ARKTS": true}}], "BUILD_MODE": "release", "USE_NORMALIZED_OHMURL": true}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": false, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"PreBuild": 180370800, "CreateModuleInfo": 2410000, "ConfigureCmake": 451300, "MergeProfile": 238814300, "CreateBuildProfile": 3374500, "PreCheckSyscap": 587200, "ProcessIntegratedHsp": 3016200, "BuildNativeWithCmake": 525400, "MakePackInfo": 9729800, "SyscapTransform": 20908800, "ProcessProfile": 121277000, "ProcessRouterMap": 25985300, "BuildNativeWithNinja": 1547400, "ProcessResource": 9234900, "GenerateLoaderJson": 124771400, "ProcessLibs": 1918316700, "DoNativeStrip": 461686100, "CompileResource": 2794482400, "BuildJS": 8123900, "CacheNativeLibs": 1415313700, "CompileArkTS": 15636191700, "GeneratePkgModuleJson": 1777800, "PackageHap": 1257028200, "SignHap": 4847131800, "CollectDebugSymbol": 11535000, "assembleHap": 287300}}, "BUILD_ID": "202507221541426210", "TOTAL_TIME": 29104324800}}