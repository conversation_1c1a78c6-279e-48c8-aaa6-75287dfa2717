// @keepTs
// @ts-nocheck
import { Method<PERSON>all, MethodChannel } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import ChannelDelegateImpl from '../../types/ChannelDelegateImpl';
import WebMessageCompatExt from '../../types/WebMessageCompatExt';
import { WebMessageChannel } from './WebMessageChannel';
export declare class WebMessageChannelChannelDelegate extends ChannelDelegateImpl {
    private webMessageChannel;
    constructor(f15: WebMessageChannel, g15: MethodChannel);
    onMethodCall(d15: MethodCall, e15: MethodResult): void;
    onMessage(b15: number, c15: WebMessageCompatExt | null): void;
    dispose(): void;
}
