// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
export default class JsConfirmResponse {
    private message;
    private confirmButtonTitle;
    private cancelButtonTitle;
    private handledByClient;
    private action;
    constructor(a20: string, b20: string, c20: string, d20: boolean, e20: number | null);
    static fromMap(z19: Map<string, Any>): JsConfirmResponse | null;
    getMessage(): string;
    setMessage(y19: string): void;
    getConfirmButtonTitle(): string;
    setConfirmButtonTitle(x19: string): void;
    getCancelButtonTitle(): string;
    setCancelButtonTitle(w19: string): void;
    isHandledByClient(): boolean;
    setHandledByClient(v19: boolean): void;
    getAction(): number | null;
    setAction(u19: number | null): void;
    equals(t19: Any): boolean;
    hashCode(): number;
    toString(): string;
}
