import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lib_base/app_initializer.dart';
import 'package:lib_base/config/route_name.dart';
import 'package:lib_base/model/http/sys_config.dart';
import 'package:lib_base/model/user_info_model.dart';
import 'package:lib_base/resource/plugins/umeng_plugin/umeng_plugin.dart';
import 'package:lib_base/widgets/common/base_scaffold.dart';
import 'package:yyb_auth/config/auth_router.dart';
import 'package:lib_base/providers/user/user_info_provider.dart';
import 'package:yyb_flutter/pages/guide/guid_page.dart';
import 'package:yyb_flutter/pages/splash/widgets/user_agree_dialog.dart';
import 'package:lib_base/config/route_utils.dart';
import 'package:lib_base/config/storage_manager.dart';
import 'package:lib_base/resource/shared_preferences_keys.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:yyb_flutter/src/generated/assets.gen.dart';
import 'package:yyb_flutter/src/api/api_repository.dart' as AppApiRepository;
import 'package:svgaplayer_flutter/svgaplayer_flutter.dart';
import 'package:lib_base/resource/plugins/exit_plugin/exit_plugin.dart';
import 'package:yyb_flutter/util/assets_util.dart';

class SplashPage extends ConsumerStatefulWidget {
  const SplashPage({super.key});

  @override
  ConsumerState<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends ConsumerState<SplashPage>
    with SingleTickerProviderStateMixin {
  late SVGAAnimationController _animationController;
  ValueNotifier<bool> _animationOver = ValueNotifier(false);

  bool _isPlay = false;
  FlutterSoundPlayer _player = FlutterSoundPlayer();
  bool _mPlayerIsInited = false;

  @override
  void initState() {
    super.initState();
    _animationController = SVGAAnimationController(vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _checkUserAgree();
      GuidPage.cacheGuidImage();
    });
  }

  Future _initPlayer() async {
    if (!_mPlayerIsInited) {
      await _player.openPlayer();
      _mPlayerIsInited = true;
    }
  }

  void _requestSysData() {
    //请求设置
    AppApiRepository.ApiRepository.findByAppId().then((response) {
      if (response.isSuccess && response.isDataNotNull) {
        SysConfig sysConfig = response.dataNotNull;
        sysConfig.saveToLocal();
      }
    });
  }

  void _initAppData() {
    // 请求系统配置
    _requestSysData();
    UserInfoModel userInfo = ref.read(userInfoNotifierProvider);
    if (userInfo.isLogin) {
      //刷新用户登录信息
      ref.read(userInfoNotifierProvider.notifier).refreshInfo().then((v) {
        //加载书本
        // ref.read(englishBookInfoProviderProvider.notifier).reqBook();
      });
    }
  }

  //用户同意用户协议以后
  void _doNextAfterAgree() async {
    _initAppData();
    _startAudio(AssetsUtils.wrapAsset(AppAssets.images.splashStartMusic));

    final videoItem = await SVGAParser.shared
        .decodeFromAssets(AppAssets.images.splashStarLogo);
    _animationController.videoItem = videoItem;
    _animationController.forward().whenComplete(() {
      _animationController.videoItem = null;
      _animationOver.value = true;
      _toNextPage();
    });
  }

  Future<void> _startAudio(String audiourl) async {
    bool isMute = StorageManager.sharedPreferences
            .getBool(SharedPreferencesKeys.splashAnimationAudio) ??
        false;
    if (!isMute) {
      await _initPlayer();
      if (!_isPlay) {
        _isPlay = true;
        _player.startPlayer(
          fromDataBuffer:
              (await rootBundle.load(audiourl)).buffer.asUint8List(),
          codec: Codec.mp3,
          whenFinished: () {
            // 播放完成回调
            _stopAudio();
          },
        );
      }
    }
  }

  Future<void> _stopAudio() async {
    if (_isPlay) {
      await _player.stopPlayer();
      _isPlay = false;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _player.stopPlayer();
    _player.closePlayer();
    super.dispose();
  }

  void _toNextPage() {
    String? token =
        StorageManager.sharedPreferences.getString(SharedPreferencesKeys.token);
    //初始化友盟
    //预初始化友盟
    UmengPlugin.preInitUmeng().then((value) {
      UmengPlugin.initCommon();
    });
    //初始化设备信息
    AppInitializer.initInfoAfterUserAgree().then((v) {});
    if (token != null) {
      //如果登录了， 就去home
      goPage(RouteName.home);
    } else {
      bool hasInitLogin = StorageManager.sharedPreferences
              .getBool(SharedPreferencesKeys.hasInitLogin) ??
          false;
      //如果没有登录， 而且没有跑过就去guide
      if (!hasInitLogin) {
        goPage(RouteName.guid);
      } else {
        //否则， 就去登录
        goPage(AuthRouteName.login);
      }
    }
  }

  void _checkUserAgree() {
    bool hasAgree = StorageManager.sharedPreferences
            .getBool(SharedPreferencesKeys.isPrivateAgreed) ??
        false;
    if (!hasAgree) {
      Future.delayed(const Duration(milliseconds: 500), () {
        UserAgreeDialog.showDialog().then((value) {
          if (value != null && value is bool) {
            if (value) {
              // 同意
              StorageManager.sharedPreferences
                  .setBool(SharedPreferencesKeys.isPrivateAgreed, true);
              //进行下一步
              _doNextAfterAgree();
            } else {
              //退出app
              ExitPlugin.exitApp();
            }
          }
        });
      });
    } else {
      _doNextAfterAgree();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BaseScaffold(
      backgroundColor: Colors.white,
      body: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Column(
          children: [
            Expanded(
              child: ValueListenableBuilder(
                valueListenable: _animationOver,
                builder: (_, over, child) {
                  return over
                      ? AppAssets.images.splashStartPic.image()
                      : SVGAImage(_animationController);
                },
              ),
            ),
            AppAssets.images.splashBottomLogo.image(
              width: 126.5.r,
              height: 40.r,
            ),
            SizedBox(height: 30.r),
          ],
        ),
      ),
    );
  }
}
