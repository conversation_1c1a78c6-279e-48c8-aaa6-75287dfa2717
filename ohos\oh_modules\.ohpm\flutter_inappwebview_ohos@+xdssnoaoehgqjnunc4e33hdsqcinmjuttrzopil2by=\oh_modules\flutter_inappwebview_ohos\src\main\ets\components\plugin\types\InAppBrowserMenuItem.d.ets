// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
export default class InAppBrowserMenuItem {
    private id;
    private title;
    private order;
    private icon;
    private iconColor;
    private showAsAction;
    constructor(v25: number, w25: string, x25: number | null, y25: Any, z25: string | null, a26: boolean);
    static fromMap(u25: Map<string, Any>): InAppBrowserMenuItem | null;
    getId(): number;
    setId(t25: number): void;
    getTitle(): string;
    setTitle(s25: string): void;
    getOrder(): number;
    setOrder(r25: number): void;
    getIcon(): Any;
    setIcon(q25: Any): void;
    getIconColor(): string;
    setIconColor(p25: string): void;
    isShowAsAction(): boolean;
    setShowAsAction(o25: boolean): void;
    equals(n25: Any): boolean;
    hashCode(): number;
    toString(): string;
}
