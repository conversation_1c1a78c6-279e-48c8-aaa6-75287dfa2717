// @keepTs
// @ts-nocheck
import { Any, MethodCall, MethodChannel } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import ChannelDelegateImpl from '../types/ChannelDelegateImpl';
import InAppWebView from './in_app_webview/InAppWebView';
import List from "@ohos.util.List";
import HitTestResult from '../types/HitTestResult';
import DownloadStartRequest from '../types/DownloadStartRequest';
import BaseCallbackResultImpl from '../types/BaseCallbackResultImpl';
import JsAlertResponse from '../types/JsAlertResponse';
import JsConfirmResponse from '../types/JsConfirmResponse';
import JsPromptResponse from '../types/JsPromptResponse';
import JsBeforeUnloadResponse from '../types/JsBeforeUnloadResponse';
import CreateWindowAction from '../types/CreateWindowAction';
import GeolocationPermissionShowPromptResponse from '../types/GeolocationPermissionShowPromptResponse';
import PermissionResponse from '../types/PermissionResponse';
import WebResourceRequestExt from '../types/WebResourceRequestExt';
import WebResourceErrorExt from '../types/WebResourceErrorExt';
import WebResourceResponseExt from '../types/WebResourceResponseExt';
import HttpAuthResponse from '../types/HttpAuthResponse';
import HttpAuthenticationChallenge from '../types/HttpAuthenticationChallenge';
import CustomSchemeResponse from '../types/CustomSchemeResponse';
import { NavigationActionPolicy } from '../types/NavigationActionPolicy';
import NavigationAction from '../types/NavigationAction';
import ServerTrustAuthResponse from '../types/ServerTrustAuthResponse';
import ServerTrustChallenge from '../types/ServerTrustChallenge';
import ClientCertResponse from '../types/ClientCertResponse';
import ClientCertChallenge from '../types/ClientCertChallenge';
import SafeBrowsingResponse from '../types/SafeBrowsingResponse';
export default class WebViewChannelDelegate extends ChannelDelegateImpl {
    private webView;
    constructor(w10: InAppWebView, x10: MethodChannel);
    onMethodCall(u10: MethodCall, v10: MethodResult): Promise<void>;
    onFindResultReceived(r10: number, s10: number, t10: boolean): void;
    onLongPressHitTestResult(q10: HitTestResult): void;
    onScrollChanged(o10: number, p10: number): void;
    onDownloadStartRequest(n10: DownloadStartRequest): void;
    onCreateContextMenu(m10: HitTestResult): void;
    onOverScrolled(i10: number, j10: number, k10: boolean, l10: boolean): void;
    onContextMenuActionItemClicked(g10: number, h10: string): void;
    onHideContextMenu(): void;
    onEnterFullscreen(): void;
    onExitFullscreen(): void;
    onJsAlert(c10: string, d10: string, e10: boolean | null, f10: JsAlertCallback): void;
    onJsConfirm(y9: string, z9: string, a10: boolean | null, b10: JsConfirmCallback): void;
    onJsPrompt(t9: string, u9: string, v9: string, w9: boolean | null, x9: JsPromptCallback): void;
    onJsBeforeUnload(q9: string, r9: string, s9: JsBeforeUnloadCallback): void;
    onCreateWindow(o9: CreateWindowAction, p9: CreateWindowCallback): void;
    onCloseWindow(): void;
    onGeolocationPermissionsShowPrompt(m9: string, n9: GeolocationPermissionsShowPromptCallback): void;
    onGeolocationPermissionsHidePrompt(): void;
    onConsoleMessage(k9: string, l9: number): void;
    onProgressChanged(j9: number): void;
    onTitleChanged(i9: string): void;
    onReceivedIcon(h9: ArrayBuffer): void;
    onReceivedTouchIconUrl(f9: string, g9: boolean): void;
    onPermissionRequest(b9: string, c9: Array<string>, d9: Any | null, e9: PermissionRequestCallback): void;
    onPermissionRequestCanceled(z8: string, a9: List<string>): void;
    onLoadStart(y8: string): void;
    onLoadStop(x8: string): void;
    onUpdateVisitedHistory(v8: string, w8: boolean): void;
    onRequestFocus(): void;
    onReceivedError(t8: WebResourceRequestExt, u8: WebResourceErrorExt): void;
    onReceivedHttpError(r8: WebResourceRequestExt, s8: WebResourceResponseExt): void;
    onReceivedHttpAuthRequest(p8: HttpAuthenticationChallenge, q8: ReceivedHttpAuthRequestCallback): Promise<void>;
    onReceivedServerTrustAuthRequest(n8: ServerTrustChallenge, o8: ReceivedServerTrustAuthRequestCallback): Promise<void>;
    onReceivedClientCertRequest(l8: ClientCertChallenge, m8: ReceivedClientCertRequestCallback): Promise<void>;
    shouldInterceptRequest(k8: WebResourceRequestExt): WebResourceResponseExt;
    onLoadResourceWithCustomScheme(j8: WebResourceRequestExt): Promise<CustomSchemeResponse>;
    shouldOverrideUrlLoading(h8: NavigationAction, i8: ShouldOverrideUrlLoadingCallback): void;
    onCallJsHandler(e8: string, f8: string, g8: CallJsHandlerCallback): void;
    onPrintRequest(b8: string, c8: string, d8: PrintRequestCallback): void;
    onZoomScaleChanged(z7: number, a8: number): void;
    onRenderProcessGone(x7: boolean, y7: number): void;
    onPageCommitVisible(w7: string): void;
    onFormResubmission(u7: string, v7: FormResubmissionCallback): void;
    onSafeBrowsingHit(r7: string, s7: ThreatType, t7: SafeBrowsingHitCallback): void;
    onRenderProcessUnresponsive(p7: string, q7: RenderProcessUnresponsiveCallback): void;
    onRenderProcessResponsive(n7: string, o7: RenderProcessResponsiveCallback): void;
}
export declare class JsAlertCallback extends BaseCallbackResultImpl<JsAlertResponse> {
    decodeResult(m7: Any): JsAlertResponse | null;
}
export declare class JsConfirmCallback extends BaseCallbackResultImpl<JsConfirmResponse> {
    decodeResult(l7: Any): JsConfirmResponse | null;
}
export declare class JsPromptCallback extends BaseCallbackResultImpl<JsPromptResponse> {
    decodeResult(k7: Any): JsPromptResponse | null;
}
export declare class JsBeforeUnloadCallback extends BaseCallbackResultImpl<JsBeforeUnloadResponse> {
    decodeResult(j7: Any): JsBeforeUnloadResponse | null;
}
export declare class CreateWindowCallback extends BaseCallbackResultImpl<Boolean> {
    decodeResult(i7: Any): Boolean | null;
}
export declare class GeolocationPermissionsShowPromptCallback extends BaseCallbackResultImpl<GeolocationPermissionShowPromptResponse> {
    decodeResult(h7: Any): GeolocationPermissionShowPromptResponse | null;
}
export declare class PermissionRequestCallback extends BaseCallbackResultImpl<PermissionResponse> {
    decodeResult(g7: Any): PermissionResponse | null;
}
export declare class ReceivedHttpAuthRequestCallback extends BaseCallbackResultImpl<HttpAuthResponse> {
    decodeResult(f7: Any): HttpAuthResponse | null;
}
export declare class ShouldOverrideUrlLoadingCallback extends BaseCallbackResultImpl<NavigationActionPolicy> {
    decodeResult(e7: Any): NavigationActionPolicy;
}
export declare class CallJsHandlerCallback extends BaseCallbackResultImpl<Any> {
    decodeResult(d7: Any): Any;
}
export declare class PrintRequestCallback extends BaseCallbackResultImpl<boolean> {
    decodeResult(c7: Any): boolean;
}
export declare class ReceivedServerTrustAuthRequestCallback extends BaseCallbackResultImpl<ServerTrustAuthResponse> {
    decodeResult(b7: Any): ServerTrustAuthResponse | null;
}
export declare class ReceivedClientCertRequestCallback extends BaseCallbackResultImpl<ClientCertResponse> {
    decodeResult(a7: Any): ClientCertResponse | null;
}
export declare class FormResubmissionCallback extends BaseCallbackResultImpl<number> {
    decodeResult(z6: Any): number | null;
}
export declare class SafeBrowsingHitCallback extends BaseCallbackResultImpl<SafeBrowsingResponse> {
    decodeResult(y6: Any): SafeBrowsingResponse | null;
}
export declare class RenderProcessUnresponsiveCallback extends BaseCallbackResultImpl<number> {
    decodeResult(x6: Any): number | null;
}
export declare class RenderProcessResponsiveCallback extends BaseCallbackResultImpl<number> {
    decodeResult(w6: Any): number | null;
}
