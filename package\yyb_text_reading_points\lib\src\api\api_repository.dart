import 'package:lib_base/config/net/base_response.dart';
import 'package:yyb_text_reading_points/model/points_info_model.dart';
import 'package:yyb_text_reading_points/model/preview_model.dart';
import 'package:yyb_text_reading_points/model/read_recite_model.dart';
import 'package:yyb_text_reading_points/model/reading_points_details_model.dart';
import 'package:yyb_text_reading_points/model/reading_points_model.dart';
import 'package:yyb_text_reading_points/model/review_model.dart';
import 'app_api.dart';

class ApiRepository {
  static Future<BaseResponse<List<ReadingPointsModel>>> listtextreadunit(
    String bookId,
  ) {
    return AppApi.instance().listtextreadunit(bookId, '0');
  }

  static Future<BaseResponse<ReadingPointsDescribeModel>> explainPhone() {
    return AppApi.instance().explainPhone('77');
  }

  static Future<BaseResponse<ExpandInfoModel>> getrecitesetting(
    String bookId,
    String userId,
  ) {
    return AppApi.instance().getrecitesetting(
      bookId,
      userId,
      '0',
    );
  }

  static Future<BaseResponse<List<ClassHourInfoModel>>> resourcereciteGetrecite(
    String id,
    String bookId,
    String type,
    String userId,
  ) {
    return AppApi.instance()
        .resourcereciteGetrecite(id, bookId, type, userId, '0', '2');
  }

  static Future<BaseResponse<PointsInfoModel>> getreciteuserscore(
    String userId,
  ) {
    return AppApi.instance().getreciteuserscore(userId);
  }

  static Future<BaseResponse<ReviewModel>> getrecitereview(
    String moduleUnitConfigId,
  ) {
    return AppApi.instance().getrecitereview(moduleUnitConfigId);
  }

  static Future<BaseResponse<PreviewModel>> getreciteprepare(
    String moduleUnitConfigId,
  ) {
    return AppApi.instance().getreciteprepare(moduleUnitConfigId);
  }

  static Future<BaseResponse<ReadReciteModel>> getrecitepart(
    String moduleUnitConfigId,
    String userId,
    String moduleType, //1背 2读
  ) {
    return AppApi.instance()
        .getrecitepart(moduleUnitConfigId, userId, moduleType);
  }

  static Future<BaseResponse> savereciteuser(
    String userId,
    String moduleUnitConfigId,
    List<String> reciteIds,
  ) {
    return AppApi.instance()
        .savereciteuser(userId, moduleUnitConfigId, reciteIds);
  }

  static Future<BaseResponse> saveuserresult({
    required String userId,
    required String moduleUnitConfigId,
    required String mp3Url,
    required String resultJson,
    required String reciteType,
    required String moduleResourceReciteId,
    required String reciteState,
    required int userScore,
    required String unitName,
  }) {
    return AppApi.instance().saveuserresult(
      userId,
      moduleUnitConfigId,
      mp3Url,
      resultJson,
      reciteType,
      moduleResourceReciteId,
      reciteState,
      userScore,
      unitName,
    );
  }
}
