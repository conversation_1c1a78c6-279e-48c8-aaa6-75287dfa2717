// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
export default class WebResourceResponseExt {
    private contentType;
    private contentEncoding;
    private statusCode;
    private reasonPhrase;
    private headers;
    private data;
    constructor(b23: string, c23: string, d23: number, e23: string, f23: Map<string, string>, g23: ArrayBuffer);
    static fromWebResourceResponse(a23: WebResourceResponse): WebResourceResponseExt;
    static fromMap(z22: Map<string, Any>): WebResourceResponseExt | null;
    toMap(): Map<string, Object>;
    getContentType(): string;
    setContentType(y22: string): void;
    getContentEncoding(): string;
    setContentEncoding(x22: string): void;
    getStatusCode(): number;
    setStatusCode(w22: number): void;
    getReasonPhrase(): string;
    setReasonPhrase(v22: string): void;
    getHeaders(): Map<string, string>;
    setHeaders(u22: Map<string, string>): void;
    getData(): ArrayBuffer;
    setData(t22: ArrayBuffer): void;
    getHeaderArray(): Array<Header>;
    toString(): string;
}
