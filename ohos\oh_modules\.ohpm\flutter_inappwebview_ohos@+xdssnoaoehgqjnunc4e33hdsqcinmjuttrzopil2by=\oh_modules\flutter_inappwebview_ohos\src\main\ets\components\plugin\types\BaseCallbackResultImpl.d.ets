// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
import ICallbackResult from './ICallbackResult';
export default class BaseCallbackResultImpl<T> implements ICallbackResult<T> {
    nonNullSuccess(i19: T): boolean;
    nullSuccess(): boolean;
    defaultBehaviour(h19: T | null): void;
    success(g19: Any): void;
    decodeResult(f19: Any): T | null;
    error(c19: string, d19: string, e19: Any): void;
    notImplemented(): void;
}
