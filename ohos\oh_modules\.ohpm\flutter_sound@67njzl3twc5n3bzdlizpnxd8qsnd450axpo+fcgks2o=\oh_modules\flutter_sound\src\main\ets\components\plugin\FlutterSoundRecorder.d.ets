// @keepTs
// @ts-nocheck
/**
 * Copyright (c) 2024 Hunan OpenValley Digital Industry Development Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { MethodCall } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import { FlutterSoundManager } from './FlutterSoundManager';
import { FlutterSoundSession } from './FlutterSoundSession';
import common from "@ohos.app.ability.common";
import { FlutterSoundRecorderCallback } from './FlutterSoundRecorderCallback';
export declare class FlutterSoundRecorder extends FlutterSoundSession implements FlutterSoundRecorderCallback {
    static ERR_UNKNOWN: string;
    static ERR_RECORDER_IS_NULL: string;
    static ERR_RECORDER_IS_RECORDING: string;
    static context: common.UIAbilityContext | undefined;
    private mRecorder;
    openRecorderCompleted(a6: boolean): void;
    closeRecorderCompleted(z5: boolean): void;
    stopRecorderCompleted(x5: boolean, y5: string): void;
    pauseRecorderCompleted(w5: boolean): void;
    resumeRecorderCompleted(v5: boolean): void;
    startRecorderCompleted(u5: boolean): void;
    updateRecorderProgressDbPeakLevel(s5: number, t5: number): void;
    recordingData(r5: ArrayBuffer): void;
    constructor(q5: MethodCall);
    getPlugin(): FlutterSoundManager;
    getStatus(): number;
    reset(o5: MethodCall, p5: MethodResult): Promise<void>;
    openRecorder(m5: MethodCall, n5: MethodResult): Promise<void>;
    closeRecorder(k5: MethodCall, l5: MethodResult): Promise<void>;
    isEncoderSupported(i5: MethodCall, j5: MethodResult): void;
    invokeMethodString(g5: string, h5: string): void;
    startRecorder(e5: MethodCall, f5: MethodResult): Promise<void>;
    stopRecorder(c5: MethodCall, d5: MethodResult): Promise<void>;
    pauseRecorder(a5: MethodCall, b5: MethodResult): Promise<void>;
    resumeRecorder(y4: MethodCall, z4: MethodResult): Promise<void>;
    setSubscriptionDuration(w4: MethodCall, x4: MethodResult): void;
    getRecordURL(u4: MethodCall, v4: MethodResult): void;
    deleteRecord(s4: MethodCall, t4: MethodResult): Promise<void>;
    setLogLevel(q4: MethodCall, r4: MethodResult): void;
}
