// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
export default class WebMessagePortCompatExt {
    private index;
    private webMessageChannelId;
    constructor(u15: number, v15: string);
    static fromMap(t15: Map<string, Any>): WebMessagePortCompatExt | null;
    toMap(): Map<string, Any>;
    getIndex(): number;
    setIndex(s15: number): void;
    getWebMessageChannelId(): string;
    setWebMessageChannelId(r15: string): void;
    equals(q15: Any): boolean;
    hashCode(): number;
    toString(): string;
}
