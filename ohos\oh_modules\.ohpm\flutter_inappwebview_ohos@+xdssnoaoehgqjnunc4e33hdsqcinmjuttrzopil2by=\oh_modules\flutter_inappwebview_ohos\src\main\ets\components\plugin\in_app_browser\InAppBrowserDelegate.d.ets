// @keepTs
// @ts-nocheck
import List from "@ohos.util.List";
import { ActivityResultListener } from './ActivityResultListener';
export interface InAppBrowserDelegate {
    getActivityResultListeners(): List<ActivityResultListener>;
    didChangeTitle(title: string): void;
    didStartNavigation(url: string): void;
    didUpdateVisitedHistory(url: string): void;
    didFinishNavigation(url: string): void;
    didFailNavigation(url: string, errorCode: number, description: string): void;
    didChangeProgress(progress: number): void;
}
