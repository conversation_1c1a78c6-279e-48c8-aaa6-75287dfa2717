// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'math_dict_list_bean.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MathDictListBean _$MathDictListBeanFromJson(Map<String, dynamic> json) =>
    MathDictListBean(
      value: json['value'] as String?,
      label: json['label'] as String?,
      moduleType: json['moduleType'] as String?,
      bookId: json['bookId'] as String?,
      isSelect: json['isSelect'] as bool?,
      dictList: (json['dictList'] as List<dynamic>?)
          ?.map((e) => DictList.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$MathDictListBeanToJson(MathDictListBean instance) =>
    <String, dynamic>{
      'value': instance.value,
      'label': instance.label,
      'moduleType': instance.moduleType,
      'bookId': instance.bookId,
      'isSelect': instance.isSelect,
      'dictList': instance.dictList,
    };

DictList _$DictListFromJson(Map<String, dynamic> json) => DictList(
      value: json['value'] as String?,
      label: json['label'] as String?,
      bookId: json['bookId'] as String?,
      isSelect: json['isSelect'] as bool?,
    );

Map<String, dynamic> _$DictListToJson(DictList instance) => <String, dynamic>{
      'value': instance.value,
      'label': instance.label,
      'bookId': instance.bookId,
      'isSelect': instance.isSelect,
    };