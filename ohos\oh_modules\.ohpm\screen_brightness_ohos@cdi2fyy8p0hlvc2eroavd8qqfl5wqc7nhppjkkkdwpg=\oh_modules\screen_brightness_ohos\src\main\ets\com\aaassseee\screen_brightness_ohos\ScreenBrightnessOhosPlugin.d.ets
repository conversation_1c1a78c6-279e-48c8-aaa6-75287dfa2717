// @keepTs
// @ts-nocheck
import { FlutterPlugin, FlutterPluginBinding, MethodCall, MethodResult, MethodCallHandler } from "@ohos/flutter_ohos";
export default class ScreenBrightnessOhosPlugin implements FlutterPlugin, MethodCallHandler {
    private methodChannel?;
    private systemScreenBrightness?;
    private applicationScreenBrightness?;
    private abilityPluginBinding;
    private mainWindow;
    private systemScreenBrightnessChangedEventChannel?;
    private systemScreenBrightnessChangedStreamHandler;
    private applicationScreenBrightnessChangedEventChannel?;
    private applicationScreenBrightnessChangedStreamHandler;
    private isAutoReset;
    private isAnimate;
    onMethodCall(b1: MethodCall, c1: MethodResult): void;
    getUniqueClassName(): string;
    onAttachedToEngine(z: FlutterPluginBinding): void;
    onDetachedFromEngine(y: FlutterPluginBinding): void;
    private getWindow;
    private getSystemScreenBrightness;
    private handleGetSystemScreenBrightnessMethodCall;
    private handleSetSystemScreenBrightnessMethodCall;
    private handleGetApplicationScreenBrightnessMethodCall;
    private handleSetApplicationScreenBrightnessMethodCall;
    private handleResetApplicationScreenBrightnessMethodCall;
    private handleApplicationScreenBrightnessChanged;
    private handleHasApplicationScreenBrightnessChangedMethodCall;
    private handleIsAutoResetMethodCall;
    private handleSetAutoResetMethodCall;
    private handleIsAnimateMethodCall;
    private handleSetAnimateMethodCall;
    private handleCanChangeSystemBrightness;
}
