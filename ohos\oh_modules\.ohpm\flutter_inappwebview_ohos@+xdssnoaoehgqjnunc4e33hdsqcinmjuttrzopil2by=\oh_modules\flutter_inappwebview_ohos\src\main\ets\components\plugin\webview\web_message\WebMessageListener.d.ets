// @keepTs
// @ts-nocheck
import { Any, BinaryMessenger } from '@ohos/flutter_ohos';
import { Disposable } from '../../types/Disposable';
import InAppWebViewInterface from '../InAppWebViewInterface';
import WebMessageListenerChannelDelegate from './WebMessageListenerChannelDelegate';
import WebMessageCompatExt from '../../types/WebMessageCompatExt';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
export default class WebMessageListener implements Disposable {
    id: string;
    jsObjectName: string;
    allowedOriginRules: Set<string>;
    webView: InAppWebViewInterface | null;
    channelDelegate: WebMessageListenerChannelDelegate | null;
    constructor(n13: string, o13: InAppWebViewInterface, p13: BinaryMessenger | null, q13: string, r13: Set<string>);
    initJsInstance(): void;
    static fromMap(k13: InAppWebViewInterface, l13: BinaryMessenger | null, m13: Map<string, Any> | null): WebMessageListener | null;
    postMessageForInAppWebView(i13: WebMessageCompatExt | null, j13: MethodResult): void;
    dispose(): void;
}
