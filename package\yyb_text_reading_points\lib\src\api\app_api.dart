import 'package:dio/dio.dart';
import 'package:lib_base/config/net/base_response.dart';
import 'package:retrofit/http.dart';
import 'package:lib_base/config/net/dio.dart';
import 'package:lib_base/config/net/interceptors/http_extra_key.dart';
import 'package:retrofit/dio.dart';
import 'package:yyb_text_reading_points/model/points_info_model.dart';
import 'package:yyb_text_reading_points/model/preview_model.dart';
import 'package:yyb_text_reading_points/model/read_recite_model.dart';
import 'package:yyb_text_reading_points/model/reading_points_details_model.dart';
import 'package:yyb_text_reading_points/model/reading_points_model.dart';
import 'package:yyb_text_reading_points/model/review_model.dart';

import 'api.dart';

part 'app_api.g.dart';

@RestApi()
abstract class AppApi {
  static AppApi? _instance;

  factory AppApi.instance() {
    return AppApi();
  }

  factory AppApi() {
    _instance ??= _AppApi(dio);
    return _instance!;
  }

  @GET(Api.listtextreadunit)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<ReadingPointsModel>>> listtextreadunit(
    @Query("bookId") String bookId,
    @Query("terminalType") String terminalType,
  );

  @GET(Api.explainPhone)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<ReadingPointsDescribeModel>> explainPhone(
    @Query("type") String type,
  );

  @GET(Api.getrecitesetting)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<ExpandInfoModel>> getrecitesetting(
    @Query("bookId") String bookId,
    @Query("userId") String userId,
    @Query("terminalType") String terminalType,
  );

  @GET(Api.resourcereciteGetrecite)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<List<ClassHourInfoModel>>> resourcereciteGetrecite(
    @Query("id") String id,
    @Query("bookId") String bookId,
    @Query("type") String type,
    @Query("userId") String userId,
    @Query("terminalType") String terminalType,
    @Query("phoneType") String phoneType,
  );

  @GET(Api.getreciteuserscore)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: false,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<PointsInfoModel>> getreciteuserscore(
    @Query("userId") String userId,
  );

  @GET(Api.getrecitereview)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<ReviewModel>> getrecitereview(
    @Query("moduleUnitConfigId") String moduleUnitConfigId,
  );

  @GET(Api.getreciteprepare)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<PreviewModel>> getreciteprepare(
    @Query("moduleUnitConfigId") String moduleUnitConfigId,
  );

  @GET(Api.getrecitepart)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse<ReadReciteModel>> getrecitepart(
    @Query("moduleUnitConfigId") String moduleUnitConfigId,
    @Query("userId") String userId,
    @Query("moduleType") String moduleType,
  );

  @POST(Api.savereciteuser)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> savereciteuser(
    @Field("userId") String userId,
    @Field("moduleUnitConfigId") String moduleUnitConfigId,
    @Field("reciteIds") List<String> reciteIds,
  );

  @POST(Api.saveuserresult)
  @Extra(<String, bool>{
    HttpExtraKey.autoLoading: true,
    HttpExtraKey.withoutLogin: false,
    HttpExtraKey.needErrorToast: true,
  })
  Future<BaseResponse> saveuserresult(
    @Field("userId") String userId,
    @Field("moduleUnitConfigId") String moduleUnitConfigId,
    @Field("saveuserresult") String saveuserresult,
    @Field("resultJson") String resultJson,
    @Field("reciteType") String reciteType,
    @Field("moduleResourceReciteId") String moduleResourceReciteId,
    @Field("reciteState") String reciteState,
    @Field("userScore") int userScore,
    @Field("unitName") String unitName,
  );
}
