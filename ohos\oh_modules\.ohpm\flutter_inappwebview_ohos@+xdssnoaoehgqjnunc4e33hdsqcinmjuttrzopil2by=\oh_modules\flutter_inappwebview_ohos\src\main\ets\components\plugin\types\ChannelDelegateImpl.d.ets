// @keepTs
// @ts-nocheck
import { MethodCall, MethodChannel } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import { IChannelDelegate } from './IChannelDelegate';
export default class ChannelDelegateImpl implements IChannelDelegate {
    private channel;
    constructor(w1: MethodChannel);
    getChannel(): MethodChannel;
    dispose(): void;
    onMethodCall(u1: MethodCall, v1: MethodResult): void;
}
