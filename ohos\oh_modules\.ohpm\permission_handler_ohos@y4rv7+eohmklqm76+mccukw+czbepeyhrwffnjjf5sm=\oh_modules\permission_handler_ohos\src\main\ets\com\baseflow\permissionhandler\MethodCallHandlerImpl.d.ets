// @keepTs
// @ts-nocheck
import { <PERSON><PERSON>all<PERSON><PERSON><PERSON>, MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import common from '@ohos.app.ability.common';
import MethodCall from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodCall';
import { AppSettingManager } from './AppSettingManager';
import { PermissionManager } from './PermissionManager';
import { ServiceManager } from './ServiceManager';
import UIAbility from '@ohos.app.ability.UIAbility';
export declare class MethodCallHandlerImpl implements MethodCallHandler {
    private applicationContext;
    private permissionManager;
    private appSettingManager;
    private serviceManager;
    private ability;
    constructor(e1: common.Context, f1: AppSettingManager, g1: PermissionManager, h1: ServiceManager);
    setAbility(d1: UIAbility | null): void;
    onMethodCall(b1: MethodCall, c1: MethodResult): void;
}
