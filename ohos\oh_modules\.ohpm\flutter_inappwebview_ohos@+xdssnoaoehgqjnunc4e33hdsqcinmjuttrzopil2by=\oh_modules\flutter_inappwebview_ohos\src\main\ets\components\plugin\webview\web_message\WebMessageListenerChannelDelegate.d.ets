// @keepTs
// @ts-nocheck
import { Method<PERSON>all, MethodChannel } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import ChannelDelegateImpl from '../../types/ChannelDelegateImpl';
import WebMessageCompatExt from '../../types/WebMessageCompatExt';
import WebMessageListener from './WebMessageListener';
export default class WebMessageListenerChannelDelegate extends ChannelDelegateImpl {
    private webMessageListener;
    constructor(p17: WebMessageListener, q17: MethodChannel);
    onMethodCall(n17: MethodCall, o17: MethodResult): void;
    onPostMessage(k17: WebMessageCompatExt, l17: string, m17: boolean): void;
    dispose(): void;
}
