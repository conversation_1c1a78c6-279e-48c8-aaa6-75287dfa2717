// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
import URLRequest from './URLRequest';
export default class NavigationAction {
    request: URLRequest;
    isForMainFrame: boolean;
    hasGesture: boolean;
    isRedirect: boolean;
    constructor(m24: URLRequest, n24: boolean, o24: boolean, p24: boolean);
    toMap(): Map<string, Any>;
    getRequest(): URLRequest;
    setRequest(l24: URLRequest): void;
    getForMainFrame(): boolean;
    setForMainFrame(k24: boolean): void;
    isHasGesture(): boolean;
    setHasGesture(j24: boolean): void;
    getRedirect(): boolean;
    setRedirect(i24: boolean): void;
    equals(h24: Any): boolean;
    toString(): string;
}
