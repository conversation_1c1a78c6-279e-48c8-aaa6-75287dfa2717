// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
export default class JsAlertResponse {
    private message;
    private confirmButtonTitle;
    private handledByClient;
    private action;
    constructor(p19: string, q19: string, r19: boolean, s19: number | null);
    static fromMap(o19: Map<string, Any>): JsAlertResponse | null;
    getMessage(): string;
    setMessage(n19: string): void;
    getConfirmButtonTitle(): string;
    setConfirmButtonTitle(m19: string): void;
    isHandledByClient(): boolean;
    setHandledByClient(l19: boolean): void;
    getAction(): number | null;
    setAction(k19: number | null): void;
    equals(j19: Any): boolean;
    hashCode(): number;
    toString(): string;
}
