// @keepTs
// @ts-nocheck
export default class MediaSizeExt {
    private id;
    private label;
    private widthMils;
    private heightMils;
    constructor(e16: string, f16: string | null, g16: number, h16: number);
    static fromMap(d16: Map<String, Object>): MediaSizeExt | null;
    toMap(): Map<String, Object>;
    getId(): string;
    setId(c16: string): void;
    getLabel(): string;
    setLabel(b16: string): void;
    getWidthMils(): number;
    setWidthMils(a16: number): void;
    getHeightMils(): number;
    setHeightMils(z15: number): void;
    equals(y15: Object): boolean;
    hashCode(): number;
    toString(): string;
}
