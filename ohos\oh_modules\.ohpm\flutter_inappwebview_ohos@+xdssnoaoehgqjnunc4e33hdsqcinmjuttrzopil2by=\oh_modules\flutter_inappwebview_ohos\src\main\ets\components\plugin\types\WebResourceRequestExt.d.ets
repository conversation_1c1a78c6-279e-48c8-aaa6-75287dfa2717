// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
export default class WebResourceRequestExt {
    private url;
    private headers;
    private isRedirect;
    private hasGesture;
    private isForMainFrame;
    private method;
    constructor(i22: string, j22: Map<string, string>, k22: boolean, l22: boolean, m22: boolean, n22: string);
    static fromWebResourceRequest(h22: WebResourceRequest): WebResourceRequestExt;
    toMap(): Map<string, Any>;
    getUrl(): string;
    setUrl(g22: string): void;
    getHeaders(): Map<string, string>;
    setHeaders(f22: Map<string, string>): void;
    getRedirect(): boolean;
    setRedirect(e22: boolean): void;
    isHasGesture(): boolean;
    setHasGesture(d22: boolean): void;
    getForMainFrame(): boolean;
    setForMainFrame(c22: boolean): void;
    getMethod(): string;
    setMethod(b22: string): void;
    toString(): string;
}
