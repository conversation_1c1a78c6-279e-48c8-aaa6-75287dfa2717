// @keepTs
// @ts-nocheck
import { IPlayer } from './IPlayer';
import { t_PLAYER_STATE } from '../plugin/FlutterSoundTypes';
import { FlutterSoundPlayerCallback } from '../plugin/FlutterSoundPlayerCallback';
export declare class FlutterAudioRenderer implements IPlayer {
    private static sampleRateMap;
    private static channelMap;
    private renderModel;
    private callback;
    private rendererVolume;
    private rendererSpeed;
    private bufferSize;
    private targetBufferSize;
    private file;
    private writeDataCallback;
    constructor(j1: FlutterSoundPlayerCallback);
    startPlayer(e1: string, f1: number, g1: number, h1: number, i1: boolean): Promise<void>;
    stopPlayer(): Promise<void>;
    pausePlayer(): Promise<void>;
    resumePlayer(): Promise<void>;
    seekTo(d1: number): void;
    setVolume(c1: number): void;
    setSpeed(b1: number): void;
    get duration(): number;
    get position(): number;
    get state(): t_PLAYER_STATE;
    feed(a1: ArrayBuffer): number;
    isPlaying(): boolean;
    private setPlayerCallback;
    private setPlayerConfig;
}
