import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lib_base/config/route_utils.dart';
import 'package:lib_base/config/theme_config.dart';
import 'package:lib_base/utils/ui_util.dart';
import 'package:yyb_text_reading_points/src/generated/assets.gen.dart';

class BackRemindPopup extends ConsumerStatefulWidget {
  const BackRemindPopup({
    super.key,
  });

  @override
  ConsumerState<BackRemindPopup> createState() => _BackRemindPopupState();
}

class _BackRemindPopupState extends ConsumerState<BackRemindPopup> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 260.r,
      height: 200.r,
      child: Stack(
        children: [
          Column(
            children: [
              Assets.images.chineseItemBgTop.image(),
              Expanded(
                child:
                    Assets.images.chineseItemBgMiddle.image(fit: BoxFit.fill),
              ),
              Assets.images.chineseItemBgBottom.image(),
            ],
          ),
          Column(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(vertical: 10.r),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Assets.images.chinesePrEBaoLove.image(width: 30.r),
                    Text(
                      '温馨提示',
                      style: ThemeConfig.currentTheme.text17M,
                    ),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 30.r, vertical: 20.r),
                child: Text(
                  '中途退出音频将会清空，不保留朗读录音。',
                  style: TextStyle(
                    fontSize: 15.sp,
                    color: Color(0xFF666666),
                  ),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildButton('去意已决', onTap: () {
                    back();
                  }),
                  _buildButton('容我三思'),
                ],
              )
            ],
          )
        ],
      ),
    );
  }

  Widget _buildButton(String title, {VoidCallback? onTap}) {
    return InkWell(
      onTap: () {
        dismissDialog();
        onTap?.call();
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          Assets.images.chineseButtomBg.image(width: 95.r),
          Text(
            '$title',
            style: TextStyle(
              fontSize: 15.sp,
              color: Color(0xFFFFF9DC),
            ),
          )
        ],
      ),
    );
  }
}
