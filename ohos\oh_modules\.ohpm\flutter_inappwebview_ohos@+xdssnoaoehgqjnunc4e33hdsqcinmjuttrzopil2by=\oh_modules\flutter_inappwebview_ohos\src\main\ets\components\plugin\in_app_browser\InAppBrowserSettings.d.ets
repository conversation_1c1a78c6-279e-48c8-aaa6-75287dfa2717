// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
import ISettings from '../ISettings';
import InAppBrowserAbility from './InAppBrowserAbility';
export default class InAppBrowserSettings implements ISettings<InAppBrowserAbility> {
    hidden: boolean;
    hideToolbarTop: boolean;
    toolbarTopBackgroundColor: string | null;
    toolbarTopFixedTitle: string | null;
    hideUrlBar: boolean;
    hideProgressBar: boolean;
    hideTitleBar: boolean;
    closeOnCannotGoBack: boolean;
    allowGoBackWithBackButton: boolean;
    shouldCloseOnBackButtonPressed: boolean;
    hideDefaultMenuItems: boolean;
    constructor();
    parse(g18: Map<string, Any>): InAppBrowserSettings;
    toMap(): Map<string, Any>;
    getRealSettings(f18: InAppBrowserAbility): Map<string, Any>;
}
