// @keepTs
// @ts-nocheck
import { MethodCall } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import InAppWebViewFlutterPlugin from '../InAppWebViewFlutterPlugin';
import ChannelDelegateImpl from '../types/ChannelDelegateImpl';
import { FlutterWebView } from './in_app_webview/FlutterWebView';
import { Context as Context } from "@ohos.abilityAccessCtrl";
import web_webview from '@ohos.web.webview';
export declare class InAppWebViewManager extends ChannelDelegateImpl {
    private plugin;
    keepAliveWebViews: Map<string, FlutterWebView | undefined>;
    controller: web_webview.WebviewController;
    windowWebViewMessages: Map<number, ControllerHandler>;
    windowAutoincrementId: number;
    debuggingEnabled: boolean;
    constructor(l1: InAppWebViewFlutterPlugin);
    onMethodCall(j1: MethodCall, k1: MethodResult): void;
    disposeKeepAlive(i1: string): void;
    clearAllCache(g1: Context, h1: boolean): void;
    dispose(): void;
}
