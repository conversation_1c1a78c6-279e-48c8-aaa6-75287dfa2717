// @keepTs
// @ts-nocheck
import InAppWebViewFlutterPlugin from '../InAppWebViewFlutterPlugin';
import { Disposable } from '../types/Disposable';
import PrintJobController from './PrintJobController';
export default class PrintJobManager implements Disposable {
    plugin: InAppWebViewFlutterPlugin | null;
    jobs: Map<string, PrintJobController>;
    constructor(b1: InAppWebViewFlutterPlugin);
    dispose(): void;
}
