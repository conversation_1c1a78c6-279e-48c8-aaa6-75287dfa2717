// @keepTs
// @ts-nocheck
import PluginScript from '../types/PluginScript';
export default class InterceptAjaxRequestJS {
    static INTERCEPT_AJAX_REQUEST_JS_PLUGIN_SCRIPT_GROUP_NAME: string;
    static FLAG_VARIABLE_FOR_SHOULD_INTERCEPT_AJAX_REQUEST_JS_SOURCE: string;
    static FLAG_VARIABLE_FOR_INTERCEPT_ONLY_ASYNC_AJAX_REQUESTS_JS_SOURCE: string;
    static createInterceptOnlyAsyncAjaxRequestsPluginScript(f13: boolean): PluginScript;
    static INTERCEPT_AJAX_REQUEST_JS_SOURCE: string;
    static INTERCEPT_AJAX_REQUEST_JS_PLUGIN_SCRIPT: PluginScript;
}
