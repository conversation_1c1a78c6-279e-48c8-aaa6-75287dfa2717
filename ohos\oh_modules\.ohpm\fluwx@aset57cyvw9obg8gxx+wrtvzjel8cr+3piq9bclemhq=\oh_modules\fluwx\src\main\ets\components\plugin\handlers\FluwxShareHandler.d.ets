// @keepTs
// @ts-nocheck
import { <PERSON><PERSON>all, MethodResult } from "@ohos/flutter_ohos";
export declare class FluwxShareHandler {
    share(s: MethodCall, t: MethodResult): void;
    shareText(q: MethodCall, r: MethodResult): Promise<void>;
    shareImage(o: Method<PERSON>all, p: MethodResult): Promise<void>;
    shareWebPage(m: MethodCall, n: MethodResult): Promise<void>;
    shareMiniProgram(k: <PERSON><PERSON>all, l: MethodResult): Promise<void>;
}
