// @keepTs
// @ts-nocheck
import cert from "@ohos.security.cert";
export default class URLProtectionSpace {
    private id;
    private host;
    private protocol;
    private realm;
    private port;
    private sslCertificate;
    private sslError;
    constructor(f27: string, g27: string, h27: string | null, i27: number, j27?: number | null, k27?: cert.X509Cert | null, l27?: SslError | null);
    toMap(): Promise<Map<string, any>>;
    getId(): number | null;
    setId(e27: number | null): void;
    getHost(): string;
    setHost(d27: string): void;
    getProtocol(): string;
    setProtocol(c27: string): void;
    getRealm(): string | null;
    setRealm(b27: string): void;
    getPort(): number;
    setPort(a27: number): void;
    getSslCertificate(): cert.X509Cert | null;
    setSslCertificate(z26: cert.X509Cert | null): void;
    getSslError(): SslError | null;
    setSslError(y26: SslError | null): void;
    toString(): string;
}
