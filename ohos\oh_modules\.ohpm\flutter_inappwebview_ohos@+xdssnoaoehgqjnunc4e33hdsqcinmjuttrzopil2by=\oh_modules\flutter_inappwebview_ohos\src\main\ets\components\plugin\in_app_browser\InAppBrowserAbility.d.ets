// @keepTs
// @ts-nocheck
import UIAbility from '@ohos.app.ability.UIAbility';
import window from '@ohos.window';
import Want from '@ohos.app.ability.Want';
import AbilityConstant from '@ohos.app.ability.AbilityConstant';
import PullToRefreshLayout from '../pull_to_refresh/PullToRefreshLayout';
import { Disposable } from '../types/Disposable';
import InAppWebView from '../webview/in_app_webview/InAppWebView';
import { InAppBrowserDelegate } from './InAppBrowserDelegate';
import InAppBrowserSettings from './InAppBrowserSettings';
import List from "@ohos.util.List";
import { ActivityResultListener } from './ActivityResultListener';
import InAppBrowserManager from './InAppBrowserManager';
import InAppBrowserChannelDelegate from './InAppBrowserChannelDelegate';
import InAppBrowserMenuItem from '../types/InAppBrowserMenuItem';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import { Any } from '@ohos/flutter_ohos';
export default class InAppBrowserAbility extends UIAbility implements InAppBrowserDelegate, Disposable {
    windowId: string | null;
    id: string | null;
    webView: InAppWebView | null;
    pullToRefreshLayout: PullToRefreshLayout | null;
    customSettings: InAppBrowserSettings;
    isHidden: boolean;
    fromActivity: string | null;
    private activityResultListeners;
    manager: InAppBrowserManager | null;
    channelDelegate: InAppBrowserChannelDelegate | null;
    menuItems: List<InAppBrowserMenuItem>;
    onCreate(d18: Want, e18: AbilityConstant.LaunchParam): void;
    onWindowStageCreate(c18: window.WindowStage): void;
    private prepareView;
    getCustomSettings(): Map<string, Any> | null;
    setSettings(a18: InAppBrowserSettings, b18: Map<string, Any>): void;
    hide(): void;
    show(): void;
    close(z17: MethodResult): void;
    getActivityResultListeners(): List<ActivityResultListener>;
    didChangeTitle(y17: string): void;
    didStartNavigation(x17: string): void;
    didUpdateVisitedHistory(w17: string): void;
    didFinishNavigation(v17: string): void;
    didFailNavigation(s17: string, t17: number, u17: string): void;
    didChangeProgress(r17: number): void;
    dispose(): void;
}
