// @keepTs
// @ts-nocheck
import common from '@ohos.app.ability.common';
import ExifDataCopier from './ExifDataCopier';
export default class ImageResizer {
    private readonly context;
    private readonly exifDataCopier;
    constructor(w2: common.Context, x2: ExifDataCopier);
    resizeImageIfNeeded(s2: string, t2: number, u2: number, v2: number): Promise<string>;
    private calculateTargetSize;
    private createImageOnExternalDirectory;
}
