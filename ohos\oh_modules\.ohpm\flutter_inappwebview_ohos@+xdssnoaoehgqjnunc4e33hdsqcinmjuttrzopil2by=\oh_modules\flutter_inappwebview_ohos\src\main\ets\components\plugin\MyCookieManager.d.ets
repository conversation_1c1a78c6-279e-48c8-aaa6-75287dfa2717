// @keepTs
// @ts-nocheck
import ChannelDelegateImpl from './types/ChannelDelegateImpl';
import InAppWebViewFlutterPlugin from './InAppWebViewFlutterPlugin';
import { Any, MethodCall } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import List from "@ohos.util.List";
export default class MyCookieManager extends ChannelDelegateImpl {
    plugin: InAppWebViewFlutterPlugin | null;
    constructor(a1: InAppWebViewFlutterPlugin);
    onMethodCall(y: MethodCall, z: MethodResult): Promise<void>;
    setCookie(n: string, o: string, p: string, q: string, r: string, s: number | null, t: number, u: boolean, v: boolean, w: string, x: MethodResult): void;
    getCookies(m: string): Promise<List<Map<string, Any>>>;
    deleteCookie(h: string, i: string, j: string, k: string, l: MethodResult): void;
    deleteCookies(d: string, e: string, f: string, g: MethodResult): void;
    deleteAllCookies(c: MethodResult): void;
    removeSessionCookies(b: MethodResult): void;
    getCookieExpirationDate(a: number): string;
    dispose(): void;
}
