import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'package:lib_base/config/app_config.dart';
import 'package:lib_base/config/route_utils.dart';
import 'package:lib_base/model/user_info_model.dart';
import 'package:lib_base/providers/user/user_info_provider.dart';
import 'package:lib_base/utils/ui_util.dart';
import 'package:yyb_text_reading_points/config/router.dart';

import 'package:yyb_text_reading_points/model/points_info_model.dart';
import 'package:yyb_text_reading_points/model/reading_points_details_model.dart';
import 'package:yyb_text_reading_points/src/api/api_repository.dart';
import 'package:yyb_text_reading_points/view/controller/current_audio_play_controller.dart';
import 'package:yyb_text_reading_points/view/controller/text_reading_points_controller.dart';
import 'package:yyb_text_reading_points/view/pages/preview/reading_points_preview_page.dart';
import 'package:yyb_text_reading_points/view/pages/reading_points_details_page/reading_points_details_page.dart';
import 'package:yyb_text_reading_points/view/pages/reading_points_details_page/widget/explanatory_note_popup.dart';
import 'package:yyb_text_reading_points/view/pages/reading_points_details_page/widget/points_popup.dart';
import 'package:yyb_text_reading_points/view/pages/reading_points_details_page/widget/setting_popup.dart';
import 'package:yyb_text_reading_points/view/pages/recite/reading_points_recite_page.dart';
import 'package:yyb_text_reading_points/view/pages/review/reading_points_review_page.dart';
part 'reading_points_details_controller.g.dart';

@riverpod
class ReadingPointsDetailsController extends _$ReadingPointsDetailsController {
  static late UserInfoModel _userInfo;
  PageController pageController = PageController();

  ValueNotifier<ClassHourInfoModel?> _currentClassHourInfoModel =
      ValueNotifier(null);
  ValueNotifier<PointsInfoModel?> pointsInfo = ValueNotifier(null);
  ValueNotifier<bool> showRule = ValueNotifier(false);
  ValueNotifier<List<ClassHourInfoModel>> reciteList = ValueNotifier([]);
  ValueNotifier<bool> showProgressView = ValueNotifier(false);

  late CurrentAudioPlayController playController;

  int build() {
    ref.onDispose(() {
      pageController.dispose();
    });
    return 0;
  }

  late ReadingPointsDetailsPageParam _param;

  initController(ReadingPointsDetailsPageParam param) {
    _param = param;
    _userInfo = ref.read(userInfoNotifierProvider);
    playController = ref.read(currentAudioPlayControllerProvider.notifier);
    playController.initController();
    loadData();
  }

  loadData() {
    showLoading();
    getRecite('', []);
    //积分弹窗的数据
    ApiRepository.getreciteuserscore(_userInfo.userId).then((response) {
      if (response.isSuccess && response.isDataNotNull) {
        pointsInfo.value = response.dataNotNull;
      }
    });
  }

  getRecite(String type, List<ClassHourInfoModel> tempList) {
    String id = _param.moduleUnitConfigId;
    if (tempList.length != 0) {
      if (type == 'left') {
        id = tempList.first.moduleUnitConfigId ?? '';
      } else if (type == 'right') {
        id = tempList.last.moduleUnitConfigId ?? '';
      }
    }
    ApiRepository.resourcereciteGetrecite(
            id, _param.bookId, type, _userInfo.userId)
        .then((response) {
      if (response.isSuccess && response.isDataNotNull) {
        if (type == '') {
          tempList = response.dataNotNull.toList();
          getRecite('left', tempList);
        } else if (type == 'left') {
          tempList.insertAll(0, response.dataNotNull.toList());
          getRecite('left', tempList);
        } else if (type == 'right') {
          tempList.addAll(response.dataNotNull.toList());
          getRecite('right', tempList);
        }
      } else {
        if (type == '') {
          getRecite('left', tempList);
        } else if (type == 'left') {
          dismissLoading();
          setPageController(tempList); //左边都没数据了，说明page的位置准了
          getRecite('right', tempList);
        }
        reciteList.value = tempList.toList();
      }
    });
  }

  setPageController(List<ClassHourInfoModel> reciteList) {
    int initIndex = 0;
    for (var i = 0; i < reciteList.length; i++) {
      ClassHourInfoModel item = reciteList[i];
      if (item.moduleUnitConfigId == _param.moduleUnitConfigId) {
        initIndex = i;
        _currentClassHourInfoModel.value = item;
        _setAudioInfo(item);
        break;
      }
    }
    pageController.jumpToPage(initIndex);
  }

  _setAudioInfo(ClassHourInfoModel item) {
    playController.setAudioInfo(item.id ?? '', item.audioPath ?? '',
        item.audioTime ?? 0, item.unitName ?? '');
  }

  pageChanged(int index) {
    // Logger.info('滑动还没写 $index');
    if (reciteList.value.isNotEmpty) {
      ClassHourInfoModel item = reciteList.value[index];
      _currentClassHourInfoModel.value = item;
      _setAudioInfo(item);
      ref
          .read(textReadingPointsControllerProvider.notifier)
          .setCurrentReadProgres(item.moduleUnitConfigId ?? '');
    }
  }

  //注释点击
  explanatoryNoteClick(String readContent) {
    showSmartDialog(
      clickMaskDismiss: false,
      ExplanatoryNotePopup(readContent: readContent),
    );
  }

  //积分点击
  pointsClick() {
    showSmartDialog(
      clickMaskDismiss: false,
      PointsPopup(),
    );
  }

  //顶部按钮点击
  topItemClick(int type) {
    if (type == 0) {
      //生字词
      toInteractWebviewUrlPage(
        url: AppConfig.RAW_WORDS_PAGE
            .replaceAll("%1", _param.moduleUnitConfigId)
            .replaceAll("%2", _currentClassHourInfoModel.value?.unitName ?? ''),
        hasStatus: false,
      );
    } else if (type == 1) {
      //设置
      showSmartDialog(
        SettingPopup(),
        animationType: SmartAnimationType.centerFade_otherSlide,
      );
    } else if (type == 2) {
      //分享
    }
  }

  //累计学习时长帮助点击
  totalTimeHelpClick() {
    showToast('E英语宝：每日凌晨更新累计学习时长数据');
  }

  //数组排序
  settingListSort(settingList) {
    if (settingList.isEmpty) return;
    List typeList = ['ktlxzt', 'qwgd', 'qwbs', 'tbktgk', 'szctx'];
    settingList.sort((a, b) {
      int indexA = typeList.indexOf(a.type);
      int indexB = typeList.indexOf(b.type);

      // 确保两个type都在typeList中
      assert(indexA != -1 && indexB != -1, 'type不在预定义的typeList中');

      return indexA.compareTo(indexB);
    });
  }

  String getTitle(String type) {
    if (type == 'qwbs') {
      return '每课全文背诵(60分以上)';
    } else if (type == 'tbktgk') {
      return '每课课文视频观看';
    } else if (type == 'ktlxzt') {
      return '每课课堂练习做题';
    } else if (type == 'qwgd') {
      return '每课全文跟读(60分以上)';
    } else if (type == 'szctx') {
      return '生字词听写奖励';
    }
    return '未知标题';
  }

  //显示规则
  showRuleBtnClick(bool isShow) {
    showRule.value = isShow;
  }

  //底部按钮点击事件
  bottomItemClick(int type) {
    if (type == 0) {
      //朗读
      showToast('朗读点击');
    } else if (type == 1) {
      //背诵
      playController.stopAudio();
      toPage(
        RouteName.readingPointsRecitePage,
        extra: ReadingPointsRecitePageParam(
          moduleUnitConfigId:
              _currentClassHourInfoModel.value?.moduleUnitConfigId ?? '',
          unitName: _currentClassHourInfoModel.value?.unitName ?? '',
        ),
      );
    } else if (type == 2) {
      //点读
      if (playController.playModel.value.audioId ==
          _currentClassHourInfoModel.value?.id) {
        if (!playController.playModel.value.isPlay) {
          playController.playButtonClick();
        }
      } else {
        _setAudioInfo(_currentClassHourInfoModel.value!);
      }
      changeProgressBarView();
    } else if (type == 3) {
      //预习
      playController.stopAudio();
      toPage(
        RouteName.readingPointsPreviewPage,
        extra: ReadingPointsPreviewPageParam(
          moduleUnitConfigId:
              _currentClassHourInfoModel.value?.moduleUnitConfigId ?? '',
        ),
      );
    } else if (type == 4) {
      //复习
      playController.stopAudio();
      toPage(
        RouteName.readingPointsReviewPage,
        extra: ReadingPointsReviewPageParam(
          moduleUnitConfigId:
              _currentClassHourInfoModel.value?.moduleUnitConfigId ?? '',
          unitName: _currentClassHourInfoModel.value?.unitName ?? '',
        ),
      );
    }
  }

  //文本点击事件
  textClick(ReciteList item) {
    playController.setAudioInfo(
        item.id ?? '', item.mp3Url ?? '', item.mp3Time ?? 0, item.name ?? '');
  }

  //显示进度条
  changeProgressBarView() {
    showProgressView.value = !showProgressView.value;
  }
}
