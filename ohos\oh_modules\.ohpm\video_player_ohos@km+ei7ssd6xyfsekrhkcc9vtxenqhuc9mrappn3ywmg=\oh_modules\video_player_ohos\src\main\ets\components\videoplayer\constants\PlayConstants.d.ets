// @keepTs
// @ts-nocheck
/**
 * Play constants for all features.
 */
export declare class PlayConstants {
    /**
     * Playback page constant.
     */
    static readonly PLAY_SPEED: number;
    static readonly VOLUME: number;
    static readonly VOLUME_SHOW: boolean;
    static readonly BRIGHT: number;
    static readonly BRIGHT_SHOW: boolean;
    static readonly POSITION_X: number;
    static readonly POSITION_Y: number;
    static readonly HEIGHT: string;
    static readonly PLAY_PLAYER_HEIGHT: string;
    static readonly PLAY_PLAYER_HEIGHT_FULL: string;
    static readonly PLAY_PROGRESS_HEIGHT: string;
    static readonly COLUMN_HEIGHT_ONE: string;
    static readonly COLUMN_HEIGHT_TWO: string;
    static readonly MIN_ANGLE: number;
    static readonly MAX_ANGLE: number;
    static readonly MIN_VALUE: number;
    static readonly MAX_VALUE: number;
    static readonly DISAPPEAR_TIME: number;
    static readonly MARGIN_ZERO: string;
    /**
     * Playback Page Header Constant.
     */
    static readonly DX: number;
    static readonly DY: number;
    static readonly GRID_COUNT: number;
    static readonly TEXT_MARGIN_LEFT: string;
    static readonly ROW_WIDTH: string;
    static readonly POPUP_ROW_HEIGHT: string;
    static readonly POPUP_ROW_MARGIN_TOP: string;
    static readonly POPUP_DIVIDER_STROKE_WIDTH: number;
    static readonly POPUP_DIVIDER_MARGIN_RIGHT: string;
    static readonly POPUP_COLUMN_WIDTH: string;
    static readonly POPUP_COLUMN_HEIGHT: string;
    static readonly POPUP_CLOSE_TIME: number;
    /**
     * Constants for setting the playback speed.
     */
    static readonly TITLE_DIALOG_ROW_HEIGHT: string;
    static readonly TITLE_DIALOG_ROW_WIDTH: string;
    static readonly TITLE_DIALOG_COLUMNS_TEMPLATE: string;
    static readonly TITLE_DIALOG_ROWS_TEMPLATE: string;
    static readonly TITLE_DIALOG_COLUMNS_GAP: number;
    static readonly TITLE_DIALOG_ROWS_GAP: number;
    static readonly TITLE_DIALOG_COLUMN_WIDTH: string;
    /**
     * Video playback constant.
     */
    static readonly PLAYER_ID: string;
    static readonly PLAYER_TYPE: string;
    static readonly PLAYER_LIBRARY_NAME: string;
    static readonly PLAYER_SURFACE_WIDTH: number;
    static readonly PLAYER_SURFACE_HEIGHT: number;
    static readonly PLAYER_STACK_WIDTH: string;
    static readonly PLAYER_IMAGE_WIDTH: string;
    static readonly PLAYER_FIRST: number;
    static readonly PLAYER_NEXT: number;
    static readonly PLAYER_DURATION: number;
    /**
     * Video playback control constant.
     */
    static readonly CONTROL_ROW_WIDTH: string;
    static readonly CONTROL_PLAY_START: number;
    static readonly CONTROL_PLAY_PAUSE: number;
    static readonly CONTROL_NEXT: number;
    static readonly CONTROL_FIRST: number;
    /**
     * Progress bar page constant.
     */
    static readonly PROGRESS_CURRENT_TIME: string;
    static readonly PROGRESS_TOTAL_TIME: string;
    static readonly PROGRESS_PROGRESS_VAL: number;
    static readonly PROGRESS_INTERVAL: number;
    static readonly PROGRESS_STEP: number;
    static readonly PROGRESS_TRACK_THICKNESS: number;
    static readonly PROGRESS_SLIDER_WIDTH: string;
    static readonly PROGRESS_MARGIN_LEFT: string;
    static readonly PROGRESS_SEEK_TIME: number;
    static readonly PROGRESS_ROW_WIDTH: string;
    /**
     * Network video playback error notification duration
     */
    static readonly PLAY_ERROR_TIME: number;
}
