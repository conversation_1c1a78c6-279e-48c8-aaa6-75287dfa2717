import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lib_base/api/api_repository.dart';
import 'package:lib_base/config/application.dart';
import 'package:lib_base/config/route_name.dart';
import 'package:lib_base/config/route_utils.dart';
import 'package:lib_base/log/log.dart';
import 'package:lib_base/model/http/book_info_detail.dart';
import 'package:lib_base/model/http/query_my_resource_model.dart';
import 'package:lib_base/providers/books/english/english_book_info_provider.dart';
import 'package:lib_base/providers/common/common_module_need_buy_controller.dart';
import 'package:lib_base/providers/user/user_info_provider.dart';
import 'package:lib_base/resource/common_constant.dart';
import 'package:lib_base/resource/plugins/screen_plugin.dart';
import 'package:lib_base/utils/business/image_util.dart';
import 'package:lib_base/utils/ui_util.dart';
import 'package:lib_base/widgets/dialog/open_vip_dialog.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:yyb_interactive_reading/extension/find_id.dart';
import 'package:yyb_interactive_reading/extension/split.dart';
import 'package:yyb_interactive_reading/model/http/interactive_reading_model.dart';
import 'package:yyb_interactive_reading/model/http/rect_info_model.dart';
import 'package:yyb_interactive_reading/model/interactive_item_result_param.dart';
import 'package:yyb_interactive_reading/page/textbook_reading/controller/audio_play_controller.dart';
import 'package:yyb_interactive_reading/page/textbook_reading/controller/react_controller.dart';
import 'package:yyb_interactive_reading/src/api/api.dart';
import 'package:yyb_interactive_reading/src/api/api_repository.dart';
import 'package:yyb_interactive_reading/utils/eyyb_interactive_reading_file_util.dart';
import 'package:lib_base/pages/video_page/english_video_play/english_video_play_page.dart';

part 'textbook_reading_page_controller.g.dart';

class ImageSizeModel {
  final Size originSize;

  Size get displaySize {
    double displyWidth = 1.sw;
    double displayHeight = (originSize.height / originSize.width) * displyWidth;
    return Size(displyWidth, displayHeight);
  }

  ImageSizeModel({required this.originSize});
}

class ReactSettingModel {
  final bool isTranslateEnabled;
  final bool isReadEnabled;

  const ReactSettingModel(
      {required this.isTranslateEnabled, required this.isReadEnabled});

  ReactSettingModel copyWith({bool? enableRead, bool? enableTranslate}) {
    return ReactSettingModel(
        isTranslateEnabled: enableTranslate ?? isTranslateEnabled,
        isReadEnabled: enableRead ?? isReadEnabled);
  }
}

@riverpod
class TextbookReadingPageController extends _$TextbookReadingPageController {
  /// 当前单元
  late ValueNotifier<InteractiveReadingModel> unitNotifier;

  //初始化参数
  late InteractiveReadingModel param;

  //来源标识
  String? source;

  late BookInfoDetail? bookInfoDetail; // 书籍详情
  ValueNotifier<List<String>> images = ValueNotifier([]); // 图片列表

  /// 用户相关变量
  String? userId; // 用户标识
  QueryMyResourceModel? queryMyResourceModel; // 用户资源模型

  //音频播放
  late AudioPlayController audioPlayController;
  //矩形相关
  late ReactController reactController;

  //播放速度
  double get speed => audioPlayController.speed;

  //当前页码
  ValueNotifier<int> currentPageIndexNotifier = ValueNotifier(0);

  //页码是按image来的， 一张图一页, 当前页的rects
  //页码字符串和对应的rects列表的map
  Map<String, List<RectInfo>> pageRectMaps = {};

  //当前单元的rect 列表
  List<RectInfo> unitRectList = [];

  Map<String, ValueNotifier<ImageSizeModel?>> imageSizeNotifierMap = {};

  //chapterNO - 页码
  Map<String, List<String>> chapterMap = {};
  //chapterNo-chapterName
  Map<String, String> chapterNoNameMap = {};
  //chapterNo-chapterVideo
  Map<String, ChapterVideo> chapterVideoMap = {};

  PageController pageController = PageController();

  //已完成的章节
  List<String> completedChapterNos = [];

  String get currentChapterNo => _getChapterNo(currentPageIndexNotifier.value);

  String _getChapterNo(int pageIndex) {
    String cno = "";
    chapterMap.forEach((key, value) {
      if (value.contains(pageIndex.toString())) {
        cno = key;
      }
    });
    return cno;
  }

  /// 构建方法 - 返回矩形信息模型
  @override
  RectInfoModel build() {
    ref.onDispose(() {
      _dispose();
    });

    return RectInfoModel();
  }

  void _dispose() {
    pageController.dispose();

    audioPlayController.dispose();
  }

  /// 控制器初始化方法
  /// @param param 交互式阅读模型参数
  Future<void> initController(InteractiveReadingModel param) async {
    try {
      final userInfo = ref.read(userInfoNotifierProvider);
      userId = userInfo.userId;
      this.param = param;
      this.unitNotifier = ValueNotifier(param);
      audioPlayController = AudioPlayController(ref);
      reactController = ReactController(audioPlayController, this, ref);

      final englishBookInfo =
          ref.read(englishBookInfoProviderProvider.notifier).bookInfoDetail;
      bookInfoDetail = englishBookInfo;
      //检测资源是否有下载， 没有则下载
      await _checkAndDownloadUnitResource(param);
      await Future.wait([
        _loadData(param),
      ]);
    } catch (e, stack) {
      Logger.error('控制器初始化失败: $e\n$stack');
      rethrow;
    }
  }

  /// 设置来源标识
  /// @param source 来源标识，如 'englishSyn'
  void setSource(String? source) {
    this.source = source;
  }

  /// 设置屏幕方向
  void setScreenOrientation() {
    param.isHscreen == "1"
        ? ScreenPlugin.switchToLandscape()
        : ScreenPlugin.switchToPortrait();
    ;
  }

  /// 重置屏幕方向为竖屏
  void resetScreenOrientation() {
    ScreenPlugin.switchToPortrait();
  }

  /// 跳转到学习报告页面
  void toReportPage() {
    try {
      final bookId = unitNotifier.value.bookId!;
      showToast("正在跳转到学习报告页面");
      //  实现报告页面跳转逻辑
      String url = Api.PROFILE_MY_ADDRESS
          .replaceFirst("%1", userId ?? "")
          .replaceFirst("%2", bookId);
      toInteractWebviewUrlPage(
        url: url,
      );
    } catch (e) {
      Logger.error('跳转报告页面失败: $e');
    }
  }

  /// 跳转到会员中心页面
  void toMyVipPage(String modelId) {
    try {
      ref
          .read(commonModuleNeedBuyControllerProvider.notifier)
          .toMemberCenterPageForEnglish(modelId);
    } catch (e) {
      Logger.error('跳转会员中心失败: $e');
    }
  }

  /// 加载数据
  Future<void> _loadData(InteractiveReadingModel param) async {
    showLoading();
    try {
      await Future.wait([
        _loadUnitDetails(param.id!),
        _loadMyResouce(),
      ]);
    } catch (e) {
      Logger.error('加载数据失败: $e');
      rethrow;
    } finally {
      dismissLoading();
    }
  }

  /// 设置是否显示点读区域
  void saveSetting(
      {required bool enableTranslate,
      required double newSpeed,
      required bool readEnabled}) {
    reactController.saveSetting(
        enableTranslate: enableTranslate, readEnabled: readEnabled);
    audioPlayController.saveSetting(newSpeed: newSpeed);
  }

  /// 设置当前播放单元
  Future setCurrentUnit(InteractiveReadingModel param) async {
    await _loadUnitDetails(param.id!);
    this.unitNotifier.value = param;
  }

  /// 加载单元其他信息
  Future<void> _loadUnitDetails(String id) async {
    try {
      showLoading();
      _queryunitintgusercompletionprog();
      final response = await ApiRepository.listRectInfo(resourceId: id);
      if (response.isSuccess && response.isDataNotNull) {
        RectInfoModel data = response.dataNotNull;

        await _initRects(data, id).whenComplete(() => dismissLoading());
        unitRectList = data.rectInfo ?? [];
        state = data;
        //滚动到第一页
        if (currentPageIndexNotifier.value != 0) {
          Logger.info("============  jumptopage:0");
          pageController.jumpToPage(0);
        }
      }
    } catch (e) {
      dismissLoading();
      Logger.error('加载矩形信息列表失败: $e');
      rethrow;
    }
  }

  //将react 按group放入map
  Future _initRects(RectInfoModel data, String id) async {
    pageRectMaps.clear();
    imageSizeNotifierMap.forEach((key, value) {
      value.dispose();
    });

    //初始化chaptermap
    chapterMap.clear();
    chapterNoNameMap.clear();
    chapterVideoMap.clear();
    List<String> chapterNoArr = data.chapterNo?.split("|") ?? [];
    List<String> pageNos = [];
    List<String> names = [];
    List<String> videoStr = [];
    if (data.chapterTitle?.isNotEmpty ?? false) {
      names = data.chapterTitle!.split("|");
    }
    if (data.chapterVideo?.isNotEmpty ?? false) {
      videoStr = data.chapterVideo!.split("|");
    }

    for (int i = 0; i < chapterNoArr.length; i++) {
      String chapterValue = chapterNoArr[i];
      if (chapterValue.isNotEmpty) {
        pageNos = [];
        chapterMap[chapterValue] = pageNos;
        if (names.length > i) {
          chapterNoNameMap[chapterValue] = names[i];
        }
        if (videoStr.length > i) {
          String vstr = videoStr[i];
          if (vstr.isNotEmpty) {
            try {
              chapterVideoMap[chapterValue] =
                  ChapterVideo.fromJson(jsonDecode(vstr));
            } catch (e, stacktrace) {
              Logger.error(
                  "====== json解析失败, json 原文： $vstr,错误信息: $e", stacktrace);
            }
          }
        }
      }
      pageNos.add(i.toString());
    }

    imageSizeNotifierMap.clear();
    data.rectInfo?.forEach((element) {
      String key = element.group?.toString() ?? "";

      List<RectInfo>? value = pageRectMaps[key];
      if (value == null) {
        value = [];
      }
      value.add(element);
      pageRectMaps[key] = value;
    });

    List<String> imagesList = data.imageFullPath?.splitAndFilter() ?? [];
    List<String> imageNames = data.imageName?.splitAndFilter() ?? [];
    List<String> localImage = [];

    for (int i = 0; i < imagesList.length; i++) {
      String imageName = imageNames[i];
      String localImagePath = await EyybInteractiveReadingFileUtil.imagePath(
          imageName: imageName, id: id);
      imageSizeNotifierMap[localImagePath] = ValueNotifier(null);
      localImage.add(localImagePath);
    }
    if (localImage.isNotEmpty) {
      precacheImage(
          FileImage(File(localImage.first)), MApplication.getContext());
    }
    images.value = localImage;
  }

  Future loadImageOriginSize(String imagePath) async {
    var sizeModel = imageSizeNotifierMap[imagePath]?.value;
    if (sizeModel == null) {
      Size size = await ImageUtil.getImageSize(imagePath);
      imageSizeNotifierMap[imagePath]?.value = ImageSizeModel(originSize: size);
    }
  }

  /// 加载用户资源信息
  Future<void> _loadMyResouce() async {
    try {
      final userInfo = ref.read(userInfoNotifierProvider);
      if (!userInfo.isVip) return;

      final response = await BaseApiRepository.queryMyResouce(
        puserId: userInfo.userId,
        types: const ["0"],
        resourceIds: [unitNotifier.value.bookId!],
        moduleIds: ["0", unitNotifier.value.moduleId!],
      );

      if (response.isSuccess && response.isDataNotNull) {
        queryMyResourceModel = response.dataNotNull;
      }
    } catch (e) {
      Logger.error('加载用户资源信息失败: $e');
      rethrow;
    }
  }

  /// 切换单元
  Future<bool> navigateToUnit(
      List<InteractiveReadingModel> courseList, bool isNext,
      {bool isAuto = false}) async {
    String currentId = unitNotifier.value.id ?? "";
    InteractiveItemResultParam result = courseList.findAdjacentItems(currentId);
    var userInfo = ref.read(userInfoNotifierProvider);

    if (isNext ? result.isLast : result.isFirst) {
      showToast(isNext ? "没有下一个单元了" : "没有上一个单元了");
      return false;
    }

    if (!userInfo.isVip) {
      showVIPMembershipDialog();
      return false;
    }

    final item = isNext ? result.nextItem : result.previousItem;
    if (item != null) {
      if (!isAuto) {
        //返回为普通模式
        reactController.backToNormalMode();
      } else {
        if (reactController.playModeNotifier.value == PlayMode.repeatMode) {
          //返回为普通模式
          reactController.backToNormalMode();
        }
      }
      //检查和下载下一单元数据
      await _checkAndDownloadUnitResource(item);
      await setCurrentUnit(item);
      return true;
    }
    return false;
  }

  //去指定章节
  void navigateToChapter(String chapterNo) {
    List<String> pageNos = chapterMap[chapterNo] ?? [];
    if (pageNos.isNotEmpty) {
      int i = int.tryParse(pageNos.first) ?? 0;
      Logger.info("去第======= i+1页：${i + 1}");
      _animateToPage(i);
    } else {
      showToast("章节数据不存在");
    }
  }

  //检查和下载下一单元数据
  Future _checkAndDownloadUnitResource(InteractiveReadingModel item) async {
    showLoading(backDismiss: false);
    bool isNeedDownload =
        await EyybInteractiveReadingFileUtil.isNeedDownload(item.id ?? "");
    if (isNeedDownload) {
      await EyybInteractiveReadingFileUtil.downloadResource(
        item.filePath ?? "",
        item.id ?? "",
      );
    }
    dismissLoading();
  }

  onPageChanged(int index) {
    int oldPageIndex = currentPageIndexNotifier.value;
    currentPageIndexNotifier.value = index;
    if (index + 1 < images.value.length) {
      precacheImage(
          FileImage(File(images.value[index + 1])), MApplication.getContext());
    }
    _saveRecord(index, oldPageIndex);

    // 检查是否滑动到最后一页
    if (index == images.value.length - 1) {
      _onReachLastPage();
    }
  }

  /// 显示VIP会员对话框
  void showVIPMembershipDialog() {
    OpenVipAlertDialog.showDialog(
      moduleId: param.moduleId ?? "",
      subject: CommonConstant.BZZY_ENGLISH,
    );
  }

  //去到该rectinfo 所在的page
  void toRectInfoPage(RectInfo rectInfo) {
    int pageIndex = getRectPageStr(rectInfo);
    if (currentPageIndexNotifier.value != pageIndex) {
      _animateToPage(pageIndex);
    }
  }

  void _animateToPage(int index) {
    pageController.animateToPage(index,
        duration: Duration(milliseconds: 100), curve: Curves.linear);
  }

  //查询当前rect 在哪个页码
  int getRectPageStr(RectInfo rectInfo) {
    String pageKey = "";
    pageRectMaps.forEach((key, value) {
      if (value.contains(rectInfo)) {
        pageKey = key;
      }
    });
    return int.tryParse(pageKey) ?? currentPageIndexNotifier.value;
  }

  //查询已完成的章节
  Future _queryunitintgusercompletionprog() {
    var userInfo = ref.read(userInfoNotifierProvider);
    return ApiRepository.queryunitintgusercompletionprog(
            userId: userInfo.userId,
            paramId: unitNotifier.value.id ?? "",
            single: "kebendiandu")
        .then((value) {
      if (value.isSuccess && value.isDataNotNull) {
        completedChapterNos = value.dataNotNull;
      }
    });
  }

  //教材解读
  void toLearnBookVideo(ChapterVideo video) {
    var userInfo = ref.read(userInfoNotifierProvider);
    String bookName = bookInfoDetail != null
        ? "${bookInfoDetail!.gradeStr ?? ""}${bookInfoDetail!.fasciculeStr ?? ""}英语${bookInfoDetail!.versionStr ?? ""}"
        : "教材";
    String unitId = unitNotifier.value.id ?? "";
    String? chapterName = chapterNoNameMap[currentChapterNo];
    ApiRepository.savevideorecord(
        userId: userInfo.userId,
        bookId: param.bookId ?? "",
        title: bookName,
        resourceId: unitId,
        videoUrl: video.videoUrl ?? "",
        describe: "${unitNotifier.value.unitName ?? ""} ${chapterName ?? ""}");

    EnglishVideoPlayPageParam playPageParam = EnglishVideoPlayPageParam(
      bookId: bookInfoDetail?.id ?? "",
      moduleId: param.moduleId ?? "",
      promoteGroup: param.moduleId ?? "",
      // videoId: video.resourceId ?? "",
      videoName: video.chapterTitle ?? "",
      videoUrl: video.videoUrl ?? "",
      trialTime: video.trialTime ?? 0,
      totalTime: video.totalTime ?? 0,
      isFree: true,
      isProhibitScreencap: true,
    );

    toPage(RouteName.englishVideoPlayPage, extra: playPageParam);
  }

  //如果是来到了新单元，保存单元完成记录
  void _saveRecord(int index, int oldPageIndex) {
    String oldChapterNo = _getChapterNo(oldPageIndex);
    String newChapterNo = currentChapterNo;
    if (oldChapterNo != newChapterNo) {
      //进入到了新的章节
      if (!completedChapterNos.contains(oldChapterNo)) {
        //老章节未完成
        var userInfo = ref.read(userInfoNotifierProvider);
        String bookName = bookInfoDetail != null
            ? "${bookInfoDetail!.gradeStr ?? ""}${bookInfoDetail!.fasciculeStr ?? ""}英语${bookInfoDetail!.versionStr ?? ""}"
            : "教材";

        String? chapterName = chapterNoNameMap[oldChapterNo];
        String unitId = unitNotifier.value.id ?? "";
        ApiRepository.saverecord(
            userId: userInfo.userId,
            bookId: param.bookId ?? "",
            resourceId: unitId,
            title: bookName,
            describe:
                "${unitNotifier.value.unitName ?? ""} ${chapterName ?? ""}");

        // 只有当source是'englishSyn'时才调用这个方法
        if (source == 'englishSyn') {
          ApiRepository.saveuserkbddcompletionprog(
            userId: userInfo.userId,
            paramId: unitId,
            subId: oldChapterNo, // 使用章节标题， 
            isRjb: 0,
            studyTime: 0,
          );
        }

        completedChapterNos.add(oldChapterNo);
      }
    }
  }

  /// 处理到达最后一页的逻辑
  void _onReachLastPage() {
    // 调用完成阅读的接口
    _reportReadingCompletion();
  }

  /// 上报阅读完成状态
  Future<void> _reportReadingCompletion() async {
    try {
      // 调用上报接口
      // final response = await ApiRepository.report(
      //   bookId: param.bookId ?? "",
      //   unitId: param.unitId ?? "",
      // );

      // if (response.isSuccess) {
      //   Logger.info('用户已完成阅读单元：${param.unitName}，上报成功');
      // } else {
      //   Logger.error('上报阅读完成状态失败: ${response.msg ?? "未知错误"}');
      // }
    } catch (e) {
      Logger.error('上报阅读完成状态失败: $e');
    }
  }
}
