// @keepTs
// @ts-nocheck
import { Permissions } from '@ohos.abilityAccessCtrl';
type ResultCallback = (errCode: string | null, errDesc: string | null) => void;
export declare const cameraPermission: Array<Permissions>;
export declare function checkPermissions(c: Array<Permissions>): Promise<boolean>;
export declare function requestPermissions(a: Context, b: ResultCallback): Promise<void>;
export {};
