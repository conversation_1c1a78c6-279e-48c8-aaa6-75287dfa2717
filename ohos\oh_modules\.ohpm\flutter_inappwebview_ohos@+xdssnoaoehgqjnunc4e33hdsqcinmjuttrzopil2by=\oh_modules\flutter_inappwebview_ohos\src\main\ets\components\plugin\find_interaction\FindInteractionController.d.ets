// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
import InAppWebViewFlutterPlugin from '../InAppWebViewFlutterPlugin';
import { Disposable } from '../types/Disposable';
import InAppWebViewInterface from '../webview/InAppWebViewInterface';
import { FindInteractionChannelDelegate } from './FindInteractionChannelDelegate';
import { FindInteractionSettings } from './FindInteractionSettings';
export declare class FindInteractionController implements Disposable {
    webView: InAppWebViewInterface | null;
    channelDelegate: FindInteractionChannelDelegate | null;
    settings: FindInteractionSettings | null;
    searchText: string | null;
    constructor(d6: InAppWebViewInterface, e6: InAppWebViewFlutterPlugin, f6: Any, g6: FindInteractionSettings | null);
    prepare(): void;
    findAll(c6: string | null): void;
    findNext(b6: boolean): void;
    clearMatches(): void;
    dispose(): void;
}
