// @keepTs
// @ts-nocheck
/**
 * Copyright (c) 2024 Hunan OpenValley Digital Industry Development Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { FlutterPlugin, FlutterPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import { AbilityAware, AbilityPluginBinding } from '@ohos/flutter_ohos';
/** FluttersoundPlugin **/
export default class FlutterSoundPlugin implements FlutterPlugin, AbilityAware {
    private pluginBinding;
    private static context;
    constructor();
    onAttachedToAbility(e7: AbilityPluginBinding): void;
    onDetachedFromAbility(): void;
    getUniqueClassName(): string;
    onAttachedToEngine(d7: FlutterPluginBinding): void;
    onDetachedFromEngine(c7: FlutterPluginBinding): void;
}
