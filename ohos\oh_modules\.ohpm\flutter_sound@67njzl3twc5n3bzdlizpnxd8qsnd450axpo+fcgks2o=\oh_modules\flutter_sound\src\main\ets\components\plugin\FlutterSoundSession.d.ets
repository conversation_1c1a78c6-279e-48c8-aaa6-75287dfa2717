// @keepTs
// @ts-nocheck
/**
 * Copyright (c) 2024 Hunan OpenValley Digital Industry Development Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { MethodCall } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import { FlutterSoundManager } from './FlutterSoundManager';
import { t_LOG_LEVEL } from './FlutterSoundTypes';
export declare abstract class FlutterSoundSession {
    slotNo: number;
    constructor(b2: MethodCall);
    init(a2: number): void;
    abstract getPlugin(): FlutterSoundManager;
    releaseSession(): void;
    abstract getStatus(): number;
    abstract reset(y1: MethodCall, z1: MethodResult): void;
    invokeMethodWithString(v1: string, w1: boolean, x1: string): void;
    invokeMethodWithInteger(s1: string, t1: boolean, u1: number): void;
    invokeMethodWithBoolean(p1: string, q1: boolean, r1: boolean): void;
    invokeMethodWithMap(m1: string, n1: boolean, o1: Map<string, ESObject>): void;
    log(k1: t_LOG_LEVEL, l1: string): void;
}
