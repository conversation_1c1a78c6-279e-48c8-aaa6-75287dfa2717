{"lockVersion": "1.0", "settings": {"resolveConflict": true, "resolveConflictStrict": false, "installAll": true}, "overrides": {"@ohos/flutter_ohos": "file:D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\har\\flutter.har", "shared_preferences_ohos": "file:D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\har\\shared_preferences_ohos.har", "permission_handler_ohos": "file:D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\har\\permission_handler_ohos.har", "@ohos/flutter_module": "file:D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry", "path_provider_ohos": "file:D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\har\\path_provider_ohos.har", "package_info_plus": "file:D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\har\\package_info_plus.har", "flutter_inappwebview_ohos": "file:D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\har\\flutter_inappwebview_ohos.har", "url_launcher_ohos": "file:D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\har\\url_launcher_ohos.har", "video_player_ohos": "file:D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\har\\video_player_ohos.har", "flutter_sound": "file:D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\har\\flutter_sound.har", "audioplayers_ohos": "file:D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\har\\audioplayers_ohos.har", "image_picker_ohos": "file:D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\har\\image_picker_ohos.har", "connectivity_plus": "file:D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\har\\connectivity_plus.har", "device_info_plus": "file:D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\har\\device_info_plus.har", "fluwx": "file:D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\har\\fluwx.har", "mobile_scanner": "file:D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\har\\mobile_scanner.har", "audio_session": "file:D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\har\\audio_session.har", "just_audio_ohos": "file:D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\har\\just_audio_ohos.har", "screen_brightness_ohos": "file:D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\har\\screen_brightness_ohos.har"}, "overrideDependencyMap": {}, "modules": {".": {"name": "", "dependencies": {"@ohos/flutter_ohos": {"specifier": "file:har/flutter.har", "version": "file:har/flutter.har"}, "@ohos/mp4parser": {"specifier": "2.0.3-rc.1", "version": "2.0.3-rc.1"}, "@umeng/common": {"specifier": "^1.1.3", "version": "1.1.3"}, "@umeng/analytics": {"specifier": "^1.2.4", "version": "1.2.4"}}, "devDependencies": {"@ohos/hypium": {"specifier": "1.0.6", "version": "1.0.6"}}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}, "entry": {"name": "entry", "dependencies": {"class-transformer": {"specifier": "^0.5.1", "version": "0.5.1"}, "audioplayers_ohos": {"specifier": "file:har/audioplayers_ohos.har", "version": "file:har/audioplayers_ohos.har"}, "flutter_inappwebview_ohos": {"specifier": "file:har/flutter_inappwebview_ohos.har", "version": "file:har/flutter_inappwebview_ohos.har"}, "path_provider_ohos": {"specifier": "file:har/path_provider_ohos.har", "version": "file:har/path_provider_ohos.har"}, "shared_preferences_ohos": {"specifier": "file:har/shared_preferences_ohos.har", "version": "file:har/shared_preferences_ohos.har"}, "url_launcher_ohos": {"specifier": "file:har/url_launcher_ohos.har", "version": "file:har/url_launcher_ohos.har"}, "video_player_ohos": {"specifier": "file:har/video_player_ohos.har", "version": "file:har/video_player_ohos.har"}, "permission_handler_ohos": {"specifier": "file:har/permission_handler_ohos.har", "version": "file:har/permission_handler_ohos.har"}, "package_info_plus": {"specifier": "file:har/package_info_plus.har", "version": "file:har/package_info_plus.har"}, "flutter_sound": {"specifier": "file:har/flutter_sound.har", "version": "file:har/flutter_sound.har"}, "connectivity_plus": {"specifier": "file:har/connectivity_plus.har", "version": "file:har/connectivity_plus.har"}, "mobile_scanner": {"specifier": "file:har/mobile_scanner.har", "version": "file:har/mobile_scanner.har"}, "image_picker_ohos": {"specifier": "file:har/image_picker_ohos.har", "version": "file:har/image_picker_ohos.har"}, "@pdp/book": {"specifier": "file:entry/libs/book.har", "version": "file:entry/libs/book.har"}, "singsound": {"specifier": "file:entry/libs/singsound-0.0.8.har", "version": "file:entry/libs/singsound-0.0.8.har"}, "device_info_plus": {"specifier": "file:har/device_info_plus.har", "version": "file:har/device_info_plus.har"}, "fluwx": {"specifier": "file:har/fluwx.har", "version": "file:har/fluwx.har"}, "flutter_image_compress_ohos": {"specifier": "file:har/flutter_image_compress_ohos.har", "version": "file:har/flutter_image_compress_ohos.har"}, "audio_session": {"specifier": "file:har/audio_session.har", "version": "file:har/audio_session.har"}, "just_audio_ohos": {"specifier": "file:har/just_audio_ohos.har", "version": "file:har/just_audio_ohos.har"}, "screen_brightness_ohos": {"specifier": "file:har/screen_brightness_ohos.har", "version": "file:har/screen_brightness_ohos.har"}, "@ohos/flutter_ohos": {"specifier": "file:entry/har/flutter.har", "version": "file:har/flutter.har"}}, "devDependencies": {}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}}, "packages": {"@ohos/flutter_ohos@file:har/flutter.har": {"storePath": "oh_modules/.ohpm/@ohos+flutter_ohos@f9kqsf0oh0+eogbjodcyiku+2cnjmlxfqlbfyepzbuo=", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@ohos/mp4parser@2.0.3-rc.1": {"integrity": "sha512-HyasvauLEl7XBHi0thwVLmZBSbgWZrVVgo3wLb/PARVDF0YH+iHJI66ErIWHon5B9SzffBP+HQlOZSErFzubUg==", "storePath": "oh_modules/.ohpm/@ohos+mp4parser@2.0.3-rc.1", "dependencies": {"@types/libmp4parser_napi.so": "oh_modules/.ohpm/@ohos+mp4parser@2.0.3-rc.1/oh_modules/@ohos/mp4parser/src/main/cpp/types/libmp4parser_napi"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@types/libmp4parser_napi.so@file:oh_modules/.ohpm/@ohos+mp4parser@2.0.3-rc.1/oh_modules/@ohos/mp4parser/src/main/cpp/types/libmp4parser_napi": {"storePath": "oh_modules/.ohpm/@ohos+mp4parser@2.0.3-rc.1/oh_modules/@ohos/mp4parser/src/main/cpp/types/libmp4parser_napi", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@umeng/common@1.1.3": {"integrity": "sha512-0pYi/JuQzZ8Gsy97Bw5lqCwkTUIK+vImQC+4mgWYbZKH+OxKu4HOoKds0bz+9xbVIyfx88dR7lEdcGeQfR6eug==", "storePath": "oh_modules/.ohpm/@umeng+common@1.1.3", "dependencies": {"libcommon.so": "oh_modules/.ohpm/@umeng+common@1.1.3/oh_modules/@umeng/common/src/main/cpp/types/libcommon"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "libcommon.so@file:oh_modules/.ohpm/@umeng+common@1.1.3/oh_modules/@umeng/common/src/main/cpp/types/libcommon": {"storePath": "oh_modules/.ohpm/@umeng+common@1.1.3/oh_modules/@umeng/common/src/main/cpp/types/libcommon", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@umeng/analytics@1.2.4": {"integrity": "sha512-qth9hSUkHtgL9NBSZYHznmZpgU9uFK6LDKyRvqjih6+HRtOi7lXl6PblN8993x5i+hfBgSWMdDxqD0wgl7tWdQ==", "storePath": "oh_modules/.ohpm/@umeng+analytics@1.2.4", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@ohos/hypium@1.0.6": {"integrity": "sha512-bb3DWeWhYrFqj9mPFV3yZQpkm36kbcK+YYaeY9g292QKSjOdmhEIQR2ULPvyMsgSR4usOBf5nnYrDmaCCXirgQ==", "storePath": "oh_modules/.ohpm/@ohos+hypium@1.0.6", "dependencies": {}, "dynamicDependencies": {}, "dev": true, "dynamic": false, "maskedByOverrideDependencyMap": false}, "class-transformer@0.5.1": {"integrity": "sha512-SQa1Ws6hUbfC98vKGxZH3KFY0Y1lm5Zm0SY8XX9zbK7FJCyVEac3ATW0RIpwzW+oOfmHE5PMPufDG9hCfoEOMw==", "storePath": "oh_modules/.ohpm/class-transformer@0.5.1", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "audioplayers_ohos@file:har/audioplayers_ohos.har": {"storePath": "oh_modules/.ohpm/audioplayers_ohos@xvb+z09rk3vd7mkhlegeaqh54x3iakcdjb42x9fzemi=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "flutter_inappwebview_ohos@file:har/flutter_inappwebview_ohos.har": {"storePath": "oh_modules/.ohpm/flutter_inappwebview_ohos@+xdssnoaoehgqjnunc4e33hdsqcinmjuttrzopil2by=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "path_provider_ohos@file:har/path_provider_ohos.har": {"storePath": "oh_modules/.ohpm/path_provider_ohos@i52uuxzkgixcgipljxotoezo7ljmyme82ilwgmwgrki=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "shared_preferences_ohos@file:har/shared_preferences_ohos.har": {"storePath": "oh_modules/.ohpm/shared_preferences_ohos@hpo+rpvqkbg4woljnvp1nngibolagne+ltekot8lzze=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "url_launcher_ohos@file:har/url_launcher_ohos.har": {"storePath": "oh_modules/.ohpm/url_launcher_ohos@trss0ed8wyr6ikj4a7wgbyvld2t0dqtpokc2gf29s5o=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "video_player_ohos@file:har/video_player_ohos.har": {"storePath": "oh_modules/.ohpm/video_player_ohos@km+ei7ssd6xyfsekrhkcc9vtxenqhuc9mrappn3ywmg=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "permission_handler_ohos@file:har/permission_handler_ohos.har": {"storePath": "oh_modules/.ohpm/permission_handler_ohos@y4rv7+eohmklqm76+mccukw+czbepeyhrwffnjjf5sm=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "package_info_plus@file:har/package_info_plus.har": {"storePath": "oh_modules/.ohpm/package_info_plus@w64mlphu6t+rsqhcb6fbyfuu+9jimq+bygowicngogm=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "flutter_sound@file:har/flutter_sound.har": {"storePath": "oh_modules/.ohpm/flutter_sound@67njzl3twc5n3bzdlizpnxd8qsnd450axpo+fcgks2o=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "connectivity_plus@file:har/connectivity_plus.har": {"storePath": "oh_modules/.ohpm/connectivity_plus@qnu57+kbyujscglghdeenv0ewkxdzkwirnf79sjwyky=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "mobile_scanner@file:har/mobile_scanner.har": {"storePath": "oh_modules/.ohpm/mobile_scanner@pzhl+kvhugccsao7zogbsd+32dz5iv+ocbij+pzhrxy=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "image_picker_ohos@file:har/image_picker_ohos.har": {"storePath": "oh_modules/.ohpm/image_picker_ohos@q+4qovzzk5oywfthtbna9+a8ns+uamve5ptw9i+rq14=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@pdp/book@file:entry/libs/book.har": {"storePath": "oh_modules/.ohpm/@pdp+book@kwetdlm8p3fehla1xud5h+ejryzid8z0alb9exoy9vg=", "dependencies": {"dayjs": "1.11.13", "@ohos/lottie": "2.0.23", "class-transformer": "0.5.1", "reflect-metadata": "0.2.1", "@ohos/crypto-js": "2.0.4", "@pdp/swiper": "1.0.0", "@pdp/evaluation": "1.1.3", "bigdata": "oh_modules/.ohpm/@pdp+book@kwetdlm8p3fehla1xud5h+ejryzid8z0alb9exoy9vg=/oh_modules/@pdp/book/src/lib/bigdata.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "dayjs@1.11.13": {"integrity": "sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==", "storePath": "oh_modules/.ohpm/dayjs@1.11.13", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@ohos/lottie@2.0.23": {"integrity": "sha512-SiVmsyllXLcvSrh98qsXtjSoVaxy1bm8sGZzmz94DZOjdQ2zSCnTzY627EevkCCFxAA5HIbqFPUWokhc8bp/3Q==", "storePath": "oh_modules/.ohpm/@ohos+lottie@2.0.23", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "reflect-metadata@0.2.1": {"integrity": "sha512-i5lLI6iw9AU3Uu4szRNPPEkomnkjRTaVt9hy/bn5g/oSzekBSMeLZblcjP74AW0vBabqERLLIrz+gR8QYR54Tw==", "storePath": "oh_modules/.ohpm/reflect-metadata@0.2.1", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@ohos/crypto-js@2.0.4": {"integrity": "sha512-589ur6oqU1UNibqefMly2cwEeEhkSoCAA3uc+oNUwRnYYtevn/kQnO+Coi36N+VJSeeg/uFzZk1K/wUMdovpOA==", "storePath": "oh_modules/.ohpm/@ohos+crypto-js@2.0.4", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@pdp/swiper@1.0.0": {"integrity": "sha512-mtAWILSkXRuYXeldCHSjytFeyVcNO2F8wLo0rqXxdVGCfrcW4WivAXpoQpBl9ZmUel50mCupjRUEWhcNzfoKIw==", "storePath": "oh_modules/.ohpm/@pdp+swiper@1.0.0", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@pdp/evaluation@1.1.3": {"integrity": "sha512-yfPG3mqnFChWEWEvsBCZCjHjvhrYjNA4592h336eu3qDv8Q6l34WY7in1A7AR4osUWpDv+4WvqRSrSy6fkAlnQ==", "storePath": "oh_modules/.ohpm/@pdp+evaluation@1.1.3", "dependencies": {"class-transformer": "0.5.1", "singsound": "entry/libs/singsound-0.0.8.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "singsound@file:entry/libs/singsound-0.0.8.har": {"storePath": "oh_modules/.ohpm/singsound@3noxo1+ldk0a5+ax9utefojjt1ehalgj+carzztjwxy=", "dependencies": {"libsingsound.so": "oh_modules/.ohpm/singsound@3noxo1+ldk0a5+ax9utefojjt1ehalgj+carzztjwxy=/oh_modules/singsound/src/main/cpp/types/libsingsound"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "libsingsound.so@file:oh_modules/.ohpm/singsound@3noxo1+ldk0a5+ax9utefojjt1ehalgj+carzztjwxy=/oh_modules/singsound/src/main/cpp/types/libsingsound": {"storePath": "oh_modules/.ohpm/singsound@3noxo1+ldk0a5+ax9utefojjt1ehalgj+carzztjwxy=/oh_modules/singsound/src/main/cpp/types/libsingsound", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "bigdata@file:oh_modules/.ohpm/@pdp+book@kwetdlm8p3fehla1xud5h+ejryzid8z0alb9exoy9vg=/oh_modules/@pdp/book/src/lib/bigdata.har": {"storePath": "oh_modules/.ohpm/bigdata@mptnw0wkm5spww48ifee6omoheeuqmqsa3pwhpz9qpk=", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "device_info_plus@file:har/device_info_plus.har": {"storePath": "oh_modules/.ohpm/device_info_plus@k6znknfekuiajvxh8np8gwdzkpis9lb77htiologxai=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "fluwx@file:har/fluwx.har": {"storePath": "oh_modules/.ohpm/fluwx@aset57cyvw9obg8gxx+wrtvzjel8cr+3piq9bclemhq=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har", "@tencent/wechat_open_sdk": "1.0.11"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@tencent/wechat_open_sdk@1.0.11": {"integrity": "sha512-c/Yy7ydBIy3d/nRmxMPfKhIYJb7UQmwFy7+6bW6JnLvzeQmFDPOT27Dsz55/nrR7H6eyv+1bi4Y2XtSLvEDuVw==", "storePath": "oh_modules/.ohpm/@tencent+wechat_open_sdk@1.0.11", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "flutter_image_compress_ohos@file:har/flutter_image_compress_ohos.har": {"storePath": "oh_modules/.ohpm/flutter_image_compress_ohos@ml7hvqg96wpjf8gdropkzbav7nbpwijc+njxlcqu6kq=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "audio_session@file:har/audio_session.har": {"storePath": "oh_modules/.ohpm/audio_session@s5bt1r7kslgaujrhrebkvoq0c4mzijzr9kys8iy+j1g=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "just_audio_ohos@file:har/just_audio_ohos.har": {"storePath": "oh_modules/.ohpm/just_audio_ohos@bri90m8bo2d4vwn+pokoxn+hgfkstvamdig3p2tb1sq=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "screen_brightness_ohos@file:har/screen_brightness_ohos.har": {"storePath": "oh_modules/.ohpm/screen_brightness_ohos@cdi2fyy8p0hlvc2eroavd8qqfl5wqc7nhppjkkkdwpg=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}}}