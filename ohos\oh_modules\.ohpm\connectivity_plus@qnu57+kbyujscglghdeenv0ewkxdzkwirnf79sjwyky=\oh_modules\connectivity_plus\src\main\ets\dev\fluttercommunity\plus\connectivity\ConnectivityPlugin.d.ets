// @keepTs
// @ts-nocheck
import { FlutterPlugin, FlutterPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
export default class ConnectivityPlugin implements FlutterPlugin {
    private methodChannel;
    private eventChannel;
    private receiver;
    private pluginBing;
    getUniqueClassName(): string;
    onAttachedToEngine(l: FlutterPluginBinding): void;
    onDetachedFromEngine(k: FlutterPluginBinding): void;
}
