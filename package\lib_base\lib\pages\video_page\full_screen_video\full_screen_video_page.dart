import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:lib_base/log/log.dart';
import 'package:lib_base/widgets/video/full_screen_page.dart';
import 'package:lib_base/widgets/video/normal_video_player/widgets/horizontal_video.dart';

class FullScreenVideoPage extends StatelessWidget {
  final String videoUrl;
  final String? videoImageUrl;
  final bool autoPlay;
  final bool isFile;

  const FullScreenVideoPage(
      {super.key,
      required this.videoUrl,
      required this.autoPlay,
      this.videoImageUrl,
      required this.isFile});

  @override
  Widget build(BuildContext context) {
    Logger.info("==================videoUrl:$videoUrl");
    return HorizontalVideo(
          isFile: isFile,
          videoImageUrl: videoImageUrl,
          videoUrl: videoUrl,
          showFullScreenTap: false,
          autoPlay: autoPlay,
        );
  }
}
