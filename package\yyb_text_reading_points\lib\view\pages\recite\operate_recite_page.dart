import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lib_base/config/theme_config.dart';
import 'package:lib_base/widgets/common/base_app_bar.dart';
import 'package:lib_base/widgets/common/base_scaffold.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:yyb_text_reading_points/model/read_recite_model.dart';
import 'package:yyb_text_reading_points/src/generated/assets.gen.dart';
import 'package:yyb_text_reading_points/view/pages/recite/controller/operate_recite_page_controller.dart';
import 'package:yyb_text_reading_points/view/widget/reading_points_back_btn.dart';

class OperateRecitePageParam {
  final ReciteVo? reciteVo;
  OperateRecitePageParam({
    required this.reciteVo,
  });
}

class OperateRecitePage extends ConsumerStatefulWidget {
  final OperateRecitePageParam param;
  const OperateRecitePage({super.key, required this.param});

  @override
  ConsumerState<OperateRecitePage> createState() => _OperateRecitePageState();
}

class _OperateRecitePageState extends ConsumerState<OperateRecitePage> {
  late OperateRecitePageController _controller;
  @override
  void initState() {
    super.initState();
    _controller = ref.read(operateRecitePageControllerProvider.notifier);
    _controller.initController(widget.param);
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        _controller.backIntercept();
        return false;
      },
      child: BaseScaffold(
        body: _buildBody(),
      ),
    );
  }

  Widget _buildAppBar() {
    return BaseAppBar.customAppBar(
      backgroundColor: Colors.transparent,
      title: Row(
        children: [
          ReadingPointsBackBtn(
            onBack: _controller.backIntercept,
          ),
          Expanded(
            child: Text(
              '全文背诵',
              style: ThemeConfig.currentTheme.text20,
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(
            width: 35.r,
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return Stack(
      children: [
        Assets.images.chinesePrFullTextReciteBg.image(
          width: double.infinity,
          height: double.infinity,
          fit: BoxFit.fill,
        ),
        Column(
          children: [
            _buildAppBar(),
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 25.r, vertical: 10.r),
                  child: ValueListenableBuilder(
                      valueListenable: _controller.isRecording,
                      builder: (_, isRecording, child) {
                        return isRecording
                            ? SizedBox()
                            : HtmlWidget(
                                '${widget.param.reciteVo?.content}',
                                textStyle: ThemeConfig.currentTheme.text21,
                                customStylesBuilder: (element) {
                                  if (element.classes
                                      .contains('ql-align-center')) {
                                    return {
                                      'text-align': 'center',
                                    };
                                  } else {
                                    return null;
                                  }
                                },
                              );
                      }),
                ),
              ),
            ),
            _buildBottomView(),
          ],
        )
      ],
    );
  }

  Widget _buildBottomView() {
    return Container(
      padding: EdgeInsets.fromLTRB(30.r, 10.r, 30.r, 30.r),
      color: Colors.white,
      child: ValueListenableBuilder(
        valueListenable: _controller.isRecording,
        builder: (_, isRecording, child) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              InkWell(
                onTap: () {
                  _controller.playOriginalSound();
                },
                child: ValueListenableBuilder(
                  valueListenable: _controller.playMS,
                  builder: (_, playMS, child) {
                    AssetGenImage img = Assets.images.bottomBtnOriginalSound;
                    if (_controller
                            .singSoundDubbingController.isOriginPlaying &&
                        _controller.currentPlayMp3File ==
                            widget.param.reciteVo?.mp3Url) {
                      img = _controller.imagePaths[_controller.imgIndex];
                    }
                    return _buildBtnItem(img, '范音');
                  },
                ),
              ),
              InkWell(
                onTap: () {
                  _controller.playReplaySound();
                },
                child: ValueListenableBuilder(
                    valueListenable: _controller.playMS,
                    builder: (_, playMS, child) {
                      String? userMp3Url = widget.param.reciteVo?.userMp3Url;
                      AssetGenImage img = userMp3Url != null
                          ? Assets.images.bottomBtnReplayIcon
                          : Assets.images.bottomBtnUnReplayIcon;
                      if (_controller.singSoundDubbingController.isReplaying &&
                          _controller.currentPlayMp3File == userMp3Url) {
                        img =
                            _controller.replayImagePaths[_controller.imgIndex];
                      }
                      return _buildBtnItem(img, '回放');
                    }),
              ),
              InkWell(
                onTap: () {
                  _controller.soundRecordingClick();
                },
                child: Column(
                  children: [
                    Stack(
                      alignment: Alignment.center,
                      children: [
                        Assets.images.questionSpokePlay1.image(width: 58.r),
                        SizedBox(
                          width: 60.r,
                          height: 60.r,
                          child: ValueListenableBuilder(
                            valueListenable: _controller.currentProgress,
                            builder: (_, progress, child) {
                              if (progress == 0) {
                                return SizedBox.shrink();
                              }
                              int reciteTime =
                                  (widget.param.reciteVo?.reciteTime ?? 1);
                              return CircularProgressIndicator(
                                value: (reciteTime - progress) / reciteTime,
                                strokeWidth: 3.r,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Color(0xFF8FF9BD),
                                ),
                                backgroundColor: Colors.transparent,
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 5.r),
                    Text(
                      '背诵',
                      style: ThemeConfig.currentTheme.text14P1,
                    )
                  ],
                ),
              ),
              InkWell(
                onTap: () {
                  _controller.resetClick();
                },
                child: _buildBtnItem(
                    isRecording
                        ? Assets.images.bottomBtnResetIcon
                        : Assets.images.bottomBtnUnResetIcon,
                    '重录'),
              ),
              InkWell(
                onTap: () {
                  _controller.submitClick();
                },
                child: ValueListenableBuilder(
                  valueListenable: _controller.singSoundResult,
                  builder: (_, singSoundResult, child) {
                    return _buildBtnItem(
                      singSoundResult != null
                          ? Assets.images.bottomBtnSubmitIcon
                          : Assets.images.bottomBtnUnSubmitIcon,
                      '提交',
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildBtnItem(AssetGenImage genImage, String text) {
    return Column(
      children: [
        genImage.image(width: 37.r),
        SizedBox(height: 5.r),
        Text(
          text,
          style: ThemeConfig.currentTheme.text14P1,
        )
      ],
    );
  }
}
