// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
import { Disposable } from '../../types/Disposable';
import WebMessagePort from '../../types/WebMessagePort';
import InAppWebViewInterface from '../InAppWebViewInterface';
import { WebMessageChannelChannelDelegate } from './WebMessageChannelChannelDelegate';
import List from "@ohos.util.List";
import { ValueCallback } from '../../types/ValueCallback';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import WebMessageCompatExt from '../../types/WebMessageCompatExt';
import web_webview from '@ohos.web.webview';
export declare class WebMessageChannel implements Disposable {
    id: string;
    channelDelegate: WebMessageChannelChannelDelegate | null;
    compatPorts: web_webview.WebMessagePort[];
    ports: List<WebMessagePort>;
    webView: InAppWebViewInterface | null;
    constructor(p11: string, q11: InAppWebViewInterface);
    initJsInstance(n11: InAppWebViewInterface, o11: ValueCallback<WebMessageChannel>): void;
    setWebMessageCallbackForInAppWebView(k11: number, l11: WebMessageCompatExt | null, m11: MethodResult): void;
    postMessageForInAppWebView(h11: number, i11: WebMessageCompatExt, j11: MethodResult): void;
    closeForInAppWebView(f11: number, g11: MethodResult): void;
    onMessage(d11: number, e11: WebMessageCompatExt | null): void;
    toMap(): Map<string, Any>;
    dispose(): void;
}
