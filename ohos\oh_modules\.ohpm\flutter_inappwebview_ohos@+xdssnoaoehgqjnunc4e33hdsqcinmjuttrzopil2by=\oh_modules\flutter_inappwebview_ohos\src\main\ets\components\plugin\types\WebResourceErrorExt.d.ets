// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
export default class WebResourceErrorExt {
    private type;
    private description;
    constructor(r22: number, s22: string);
    toMap(): Map<string, Any>;
    static fromWebResourceError(q22: WebResourceError): WebResourceErrorExt;
    getType(): number;
    setType(p22: number): void;
    getDescription(): string;
    setDescription(o22: string): void;
    toString(): string;
}
