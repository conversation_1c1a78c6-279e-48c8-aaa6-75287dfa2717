// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
import NavigationAction from './NavigationAction';
import URLRequest from './URLRequest';
export default class CreateWindowAction extends NavigationAction {
    windowId: number;
    isDialog: boolean;
    constructor(j21: URLRequest, k21: boolean, l21: boolean, m21: boolean, n21: number, o21: boolean);
    toMap(): Map<string, Any>;
    getWindowId(): number;
    setWindowId(i21: number): void;
    getDialog(): boolean;
    setDialog(h21: boolean): void;
    toString(): string;
}
