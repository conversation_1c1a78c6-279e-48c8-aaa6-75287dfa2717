// @keepTs
// @ts-nocheck
/**
 * Copyright (c) 2024 Hunan OpenValley Digital Industry Development Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import SoundPoolPlayer from '../player/SoundPoolPlayer';
import Source from './Source';
import media from '@ohos.multimedia.media';
export default class UrlSource implements Source {
    url: string;
    private isLocal;
    constructor(p1: string, q1: boolean);
    setForMediaPlayer(o1: media.AVPlayer): void;
    setForSoundPool(n1: SoundPoolPlayer): void;
    getAudioPathForSoundPool(m1: Context): Promise<string>;
}
