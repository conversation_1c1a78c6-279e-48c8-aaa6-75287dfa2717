import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lib_base/api/api_repository.dart';
import 'package:lib_base/config/route_utils.dart';
import 'package:lib_base/config/theme_config.dart';
import 'package:lib_base/generated/assets.gen.dart';
import 'package:lib_base/log/log.dart';
import 'package:lib_base/model/http/learn_course_video_info.dart';
import 'package:lib_base/model/user_info_model.dart';
import 'package:lib_base/pages/video_page/english_video_play/widgets/exit_video_learn_dialog.dart';
import 'package:lib_base/pages/video_page/english_video_play/learn_course_video_play/widget/complete_submit_dialog.dart';
import 'package:lib_base/providers/user/user_info_provider.dart';
import 'package:lib_base/resource/plugins/window_private/window_private_plugin.dart';
import 'package:lib_base/utils/business/image_util.dart';
import 'package:lib_base/utils/ui_util.dart';
import 'package:lib_base/widgets/video/controller/m_video_player_controller.dart';
import 'package:lib_base/widgets/video/normal_video_player/widgets/m_normal_video_progress_indicator.dart';
import 'package:lib_base/widgets/video/short_video_player/widgets/video_play_button.dart';
import 'package:video_player/video_player.dart';

class LearnCourseVideoPlayPageParam {
  final String videoUrl;
  final String? tcManageId;
  final String? manageId;
  final String? trainingCampId;
  final String? courseName;
  /**
   * 禁止录屏
   */
  final bool isProhibitScreencap;

  final bool autoPlay;

  LearnCourseVideoPlayPageParam({
    required this.videoUrl,
    this.tcManageId,
    this.manageId,
    this.trainingCampId,
    this.courseName,
    this.isProhibitScreencap = true,
    this.autoPlay = true,
  });
}

class LearnCourseVideoPlayPage extends ConsumerStatefulWidget {
  final LearnCourseVideoPlayPageParam param;
  const LearnCourseVideoPlayPage({super.key, required this.param});

  @override
  ConsumerState<LearnCourseVideoPlayPage> createState() =>
      _LearnCourseVideoPlayPageState();
}

class _LearnCourseVideoPlayPageState
    extends ConsumerState<LearnCourseVideoPlayPage> {
  late MVideoPlayerController _controller;

  ValueNotifier<bool> _hideMenusNotifier = ValueNotifier(true);

  ValueNotifier<bool> _isLockedNotifier = ValueNotifier(false);

  Timer? _timer;

  //鸿蒙的播放器有bug， 初始化好以后， 播放器依然是白屏, 所以用这个控制一下
  bool _isPlayed = false;
  @override
  void initState() {
    super.initState();

    if (widget.param.isProhibitScreencap) {
      //禁止录屏
      WindowPrivatePlugin.setWindowPrivate(true);
    }
    _controller = MVideoPlayerController();
    _initController();
  }

  Future _initController() async {
    String videoUrl = widget.param.videoUrl;
    await _controller.initController(
        videoUrl: ImageUtil.getImageUrl(videoUrl), isFile: false);
    _controller.initialize().then((value) async {
      Logger.info("=======  controller initialized");

      if (widget.param.autoPlay) {
        _controller.play();
      }

      setState(() {});
    });

    // _controller.videoController?.addListener(() async {
    //   bool isPlaying = _controller.videoController!.value.isPlaying;
    //   if (isPlaying && !_isPlayed) {
    //     //鸿蒙的播放器有bug， 初始化好以后， 播放器依然是白屏, 所以用这个控制一下
    //     _isPlayed = true;
    //     setState(() {});
    //   }
    // });
  }

  @override
  void dispose() {
    super.dispose();
    if (widget.param.isProhibitScreencap) {
      //禁止录屏
      WindowPrivatePlugin.setWindowPrivate(false);
    }
    _controller.dispose();
    _timer?.cancel();
  }

  void _autoSwitchProgressVisible() {
    _hideMenusNotifier.value = !_hideMenusNotifier.value;
    if (_hideMenusNotifier.value) {
      //如果是隐藏
      _timer?.cancel();
    } else {
      //如果是显示
      _timer = Timer.periodic(Duration(seconds: 5), (timer) {
        _hideMenusNotifier.value = true;
        timer.cancel();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (bool didPop) async {
        if (!didPop) {
          await _doBack();
        }
      },
      child: SizedBox(
        width: 1.sw,
        height: 1.sh,
        child: Stack(
          alignment: Alignment.center,
          children: [
            _playerWidget(),
            //播放按钮
            _playButton(),

            _progressWidget(),
            _headerWidget(),
            _lockWidget(),
          ],
        ),
      ),
    );
  }

  Future _doBack() async {
    _controller.videoController?.pause();
    //弹出返回弹框
    showSmartDialog(ExitVideoLearnDialog(
      doContinue: () {
        dismissDialog();
        _controller.videoController?.play();
      },
      doExit: () {
        dismissDialog();
        back(_controller.value);
      },
    ));
  }

  Widget _playButton() {
    return _controller.videoController != null
        ? VideoPlayButton(
            _controller.videoController!,
            doPlay: (c) async {
              //播放
              c.play();
            },
          )
        : const SizedBox.shrink();
  }

  Widget _playerWidget() {
    return Container(
      alignment: Alignment.center,
      color: ThemeConfig.currentTheme.colorBlack,
      child: ((_controller.value?.isInitialized ?? false) && _isPlayed)
          ? AspectRatio(
              aspectRatio: _controller.value!.aspectRatio,
              child: InkWell(
                  onTap: () {
                    _autoSwitchProgressVisible();
                  },
                  child: new VideoPlayer(_controller.videoController!)))
          : Container(
              color: Colors.yellow,
            ),
    );
  }

  Widget _progressWidget() {
    return Positioned(
      bottom: 15.h,
      child: ValueListenableBuilder(
        valueListenable: _hideMenusNotifier,
        builder: (_, value, child) {
          return Offstage(
            offstage: !(_controller.value?.isInitialized ?? false) || value,
            child: child,
          );
        },
        child: (_controller.value?.isInitialized ?? false)
            ? ValueListenableBuilder(
                valueListenable: _isLockedNotifier,
                builder: (_, isLocked, c) {
                  return isLocked
                      ? const SizedBox.shrink()
                      : Container(
                          alignment: Alignment.center,
                          padding: EdgeInsets.symmetric(horizontal: 10.w),
                          width: 1.sw,
                          height: 55.h,
                          child: MNormalVideoProgressIndicator(
                            _controller.videoController!,
                            showFullScreenTap: false,
                            showSpeedControl: true,
                            allowScrubbing: true,
                            padding: EdgeInsets.symmetric(vertical: 15.h),
                            seekWidget: SeekWidgetModel(
                                seekWidget: BaseAssets
                                    .images.videoPlayerSeekIcon
                                    .image(width: 30.r, height: 30.r),
                                width: 30.r),
                            doPlay: (c) async {
                              //播放
                              c.play();
                            },
                            onFinish: () {
                              _uploadPlayCompleteRecord();
                            },
                          ),
                        );
                })
            : const SizedBox.shrink(),
      ),
    );
  }

  Widget _headerWidget() {
    return Positioned(
      left: 10,
      top: MediaQuery.of(context).padding.top,
      child: ValueListenableBuilder(
        valueListenable: _hideMenusNotifier,
        builder: (_, value, child) {
          return Offstage(
            offstage: !(_controller.value?.isInitialized ?? false) || value,
            child: child,
          );
        },
        child: InkWell(
          onTap: () {
            _doBack();
          },
          child: Container(
            padding: EdgeInsets.all(10.r),
            child: Row(
              children: [
                Icon(
                  Icons.arrow_back_ios,
                  color: ThemeConfig.currentTheme.colorWhite,
                  size: 22.sp,
                ),
                Text(
                  widget.param.courseName ?? "",
                  style: ThemeConfig.currentTheme.text17WhiteM,
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _lockWidget() {
    return Positioned(
      left: MediaQuery.of(context).padding.left + 30,
      top: 0.5.sh - 18,
      child: ValueListenableBuilder(
        valueListenable: _hideMenusNotifier,
        builder: (_, value, child) {
          return Offstage(
            offstage: !(_controller.value?.isInitialized ?? false) || value,
            child: child,
          );
        },
        child: InkWell(
          onTap: () {
            _isLockedNotifier.value = !_isLockedNotifier.value;
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: ValueListenableBuilder(
                valueListenable: _isLockedNotifier,
                builder: (_, isLocked, c) {
                  return Container(
                    padding: EdgeInsets.all(10.r),
                    child: Row(
                      children: [
                        Icon(
                          isLocked ? Icons.lock : Icons.lock_open_outlined,
                          color: ThemeConfig.currentTheme.colorWhite,
                          size: 32.sp,
                        ),
                      ],
                    ),
                  );
                }),
          ),
        ),
      ),
    );
  }

  //提交完成记录
  void _uploadPlayCompleteRecord() {
    UserInfoModel userInfo = ref.read(userInfoNotifierProvider);
    int seconds = _controller.videoController?.value.position.inSeconds ?? 0;
    BaseApiRepository.submitwatch(
      manageId: widget.param.manageId,
      trainingCampId: widget.param.trainingCampId,
      tcManageId: widget.param.tcManageId,
      userId: userInfo.userId,
      watchTime: seconds,
      watchSpeed: 100,
    ).then((value) {
      if (value.isSuccess) {
        //显示视频学习完成提示框
        var resData = value.dataNotNull;
        int point = num.tryParse(resData.point ?? "")?.toInt() ?? 0;
        showSmartDialog(
            CompleteSubmitDialog(
              point: point,
              seconds: seconds,
              toExercise: () {
                dismissDialog();
                toInteractWebviewModulePage(
                    moduleId: "74",
                    isFinish: true,
                    isLandscape: true,
                    pageName: "courseExercise.html",
                    othermap: {
                      "userId": userInfo.userId,
                      "trainingCampId": widget.param.trainingCampId,
                      "exerciseName": widget.param.courseName,
                      "manageId": widget.param.tcManageId,
                      "gmManageId": widget.param.manageId,
                    });
              },
            ),
            backDismiss: true,
            clickMaskDismiss: true);
      }
    });
  }
}
