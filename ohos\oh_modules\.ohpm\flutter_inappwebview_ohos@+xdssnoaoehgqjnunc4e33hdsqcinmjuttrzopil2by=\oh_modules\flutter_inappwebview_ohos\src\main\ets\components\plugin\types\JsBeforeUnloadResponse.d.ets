// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
export default class JsBeforeUnloadResponse {
    private message;
    private confirmButtonTitle;
    private cancelButtonTitle;
    private handledByClient;
    private action;
    constructor(c21: string, d21: string, e21: string, f21: boolean, g21: number | null);
    static fromMap(b21: Map<string, Any>): JsBeforeUnloadResponse | null;
    getMessage(): string;
    setMessage(a21: string): void;
    getConfirmButtonTitle(): string;
    setConfirmButtonTitle(z20: string): void;
    getCancelButtonTitle(): string;
    setCancelButtonTitle(y20: string): void;
    isHandledByClient(): boolean;
    setHandledByClient(x20: boolean): void;
    getAction(): number | null;
    setAction(w20: number | null): void;
    equals(v20: Any): boolean;
    hashCode(): number;
    toString(): string;
}
