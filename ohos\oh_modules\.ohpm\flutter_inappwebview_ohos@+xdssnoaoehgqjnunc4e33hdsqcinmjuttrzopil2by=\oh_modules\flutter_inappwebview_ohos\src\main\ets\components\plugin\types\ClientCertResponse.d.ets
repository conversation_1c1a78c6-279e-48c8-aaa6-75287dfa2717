// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
export default class ClientCertResponse {
    private certificatePath;
    private certificatePassword;
    private keyStoreType;
    private action;
    constructor(z24: string, a25: string, b25: string, c25: number);
    static fromMap(y24: Map<string, Any>): ClientCertResponse | null;
    getCertificatePath(): string;
    setCertificatePath(x24: string): void;
    getCertificatePassword(): string;
    setCertificatePassword(w24: string): void;
    getKeyStoreType(): string;
    setKeyStoreType(v24: string): void;
    getAction(): number;
    setAction(u24: number): void;
}
