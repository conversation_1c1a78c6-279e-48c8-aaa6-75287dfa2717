// @keepTs
// @ts-nocheck
import * as wechatOpenSDK from "@tencent/wechat_open_sdk";
import { MethodCall, MethodResult } from '@ohos/flutter_ohos';
import common from "@ohos.app.ability.common";
export declare class WXAPiHandler {
    static wxApi: wechatOpenSDK.WXApi | null;
    private static registered;
    private static context;
    static get wxApiRegistered(): boolean;
    static get uiContext(): import("fluwx/../../../../../../../../softs/DevEco/DevEco Studio/sdk/default/openharmony/ets/api/application/UIAbilityContext").default;
    static coolBoot: boolean;
    static setContext(j: common.UIAbilityContext): void;
    static registerApp(h: MethodCall, i: MethodResult): void;
    static checkWeChatInstallation(g: MethodResult): void;
    private static registerWxAPIInternal;
}
