// @keepTs
// @ts-nocheck
/**
 * Copyright (c) 2024 Hunan OpenValley Digital Industry Development Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { MethodCall } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import { FlutterSoundManager } from './FlutterSoundManager';
import { FlutterSoundSession } from './FlutterSoundSession';
import { t_PLAYER_STATE } from './FlutterSoundTypes';
import common from "@ohos.app.ability.common";
import { FlutterSoundPlayerCallback } from './FlutterSoundPlayerCallback';
export declare class FlutterSoundPlayer extends FlutterSoundSession implements FlutterSoundPlayerCallback {
    static ERR_UNKNOWN: string;
    static ERR_PLAYER_IS_NULL: string;
    static ERR_PLAYER_IS_PLAYING: string;
    static context: common.UIAbilityContext | undefined;
    private mPlayer;
    private audioFile;
    openPlayerCompleted(l4: boolean): void;
    closePlayerCompleted(k4: boolean): void;
    stopPlayerCompleted(j4: boolean): void;
    pausePlayerCompleted(i4: boolean): void;
    resumePlayerCompleted(h4: boolean): void;
    startPlayerCompleted(f4: boolean, g4: number): void;
    needSomeFood(e4: number): void;
    updateProgress(c4: number, d4: number): void;
    audioPlayerDidFinishPlaying(b4: boolean): void;
    updatePlaybackState(a4: t_PLAYER_STATE): void;
    constructor(z3: MethodCall);
    getPlugin(): FlutterSoundManager;
    getStatus(): number;
    getPlayerState(): t_PLAYER_STATE;
    openPlayer(x3: MethodCall, y3: MethodResult): Promise<void>;
    closePlayer(v3: MethodCall, w3: MethodResult): Promise<void>;
    reset(t3: MethodCall, u3: MethodResult): Promise<void>;
    startPlayerFromMic(r3: MethodCall, s3: MethodResult): void;
    startPlayer(p3: MethodCall, q3: MethodResult): Promise<void>;
    feed(n3: MethodCall, o3: MethodResult): void;
    stopPlayer(l3: MethodCall, m3: MethodResult): Promise<void>;
    isDecoderSupported(j3: MethodCall, k3: MethodResult): void;
    pausePlayer(h3: MethodCall, i3: MethodResult): Promise<void>;
    resumePlayer(f3: MethodCall, g3: MethodResult): Promise<void>;
    seekToPlayer(d3: MethodCall, e3: MethodResult): Promise<void>;
    setVolume(b3: MethodCall, c3: MethodResult): void;
    setSpeed(z2: MethodCall, a3: MethodResult): void;
    setSubscriptionDuration(x2: MethodCall, y2: MethodResult): void;
    getProgress(v2: MethodCall, w2: MethodResult): void;
    getResourcePath(t2: MethodCall, u2: MethodResult): void;
    getPlayerStateByChannel(r2: MethodCall, s2: MethodResult): void;
    setLogLevel(p2: MethodCall, q2: MethodResult): void;
}
