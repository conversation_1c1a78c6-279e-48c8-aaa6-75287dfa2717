// @keepTs
// @ts-nocheck
import { Any } from '@ohos/flutter_ohos';
export default class DownloadStartRequest {
    private url;
    private userAgent;
    private contentDisposition;
    private mimeType;
    private contentLength;
    private suggestedFilename;
    private textEncodingName;
    constructor(v18: string, w18: string, x18: string, y18: string, z18: number, a19: string | null, b19: string | null);
    toMap(): Map<string, Any>;
    getUrl(): string;
    setUrl(u18: string): void;
    getUserAgent(): string;
    setUserAgent(t18: string): void;
    getContentDisposition(): string;
    setContentDisposition(s18: string): void;
    getMimeType(): string;
    setMimeType(r18: string): void;
    getContentLength(): number;
    setContentLength(q18: number): void;
    getSuggestedFilename(): string | null;
    setSuggestedFilename(p18: string): void;
    getTextEncodingName(): string | null;
    setTextEncodingName(o18: string): void;
}
