// @keepTs
// @ts-nocheck
import URLAuthenticationChallenge from './URLAuthenticationChallenge';
import URLCredential from './URLCredential';
import URLProtectionSpace from './URLProtectionSpace';
export default class HttpAuthenticationChallenge extends URLAuthenticationChallenge {
    private previousFailureCount;
    proposedCredential: URLCredential | null;
    constructor(s23: URLProtectionSpace, t23: number, u23: URLCredential | null);
    toMap(): Promise<Map<string, any>>;
    getPreviousFailureCount(): number;
    setPreviousFailureCount(r23: number): void;
    getProposedCredential(): URLCredential | null;
    setProposedCredential(q23: URLCredential | null): void;
    toString(): string;
}
