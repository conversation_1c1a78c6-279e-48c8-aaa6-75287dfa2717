import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lib_base/config/theme_config.dart';
import 'package:lib_base/generated/assets.gen.dart';
import 'package:lib_base/utils/business/image_util.dart';
import 'package:lib_base/utils/screen_util.dart';
import 'package:lib_base/widgets/common/base_app_bar.dart';
import 'package:lib_base/widgets/common/base_scaffold.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:lib_base/widgets/image/net_cache_image.dart';
import 'package:yyb_text_reading_points/model/read_recite_model.dart';
import 'package:yyb_text_reading_points/src/generated/assets.gen.dart';
import 'package:yyb_text_reading_points/view/pages/recite/controller/reading_points_recite_page_controller.dart';
import 'package:yyb_text_reading_points/view/widget/reading_points_back_btn.dart';

class ReadingPointsRecitePageParam {
  final String moduleUnitConfigId;
  final String unitName;
  ReadingPointsRecitePageParam(
      {required this.moduleUnitConfigId, required this.unitName});
}

class ReadingPointsRecitePage extends ConsumerStatefulWidget {
  final ReadingPointsRecitePageParam param;
  const ReadingPointsRecitePage({super.key, required this.param});

  @override
  ConsumerState<ReadingPointsRecitePage> createState() =>
      _ReadingPointsRecitePageState();
}

class _ReadingPointsRecitePageState
    extends ConsumerState<ReadingPointsRecitePage> {
  late ReadingPointsRecitePageController _controller;

  @override
  void initState() {
    super.initState();
    _controller = ref.read(readingPointsRecitePageControllerProvider.notifier);
    _controller.initController(widget.param);
  }

  @override
  Widget build(BuildContext context) {
    ref.watch(readingPointsRecitePageControllerProvider.notifier);
    return BaseScaffold(
      body: _buildBody(),
    );
  }

  Widget _buildAppBar() {
    return BaseAppBar.customAppBar(
      backgroundColor: Colors.transparent,
      title: Row(
        children: [
          ReadingPointsBackBtn(),
          SizedBox(
            width: 45.r,
          ),
          Expanded(
            child: Text(
              '${widget.param.unitName}',
              style: ThemeConfig.currentTheme.text20,
              textAlign: TextAlign.center,
            ),
          ),
          Container(
            width: 80.r,
            height: 25.r,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.8),
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: InkWell(
              onTap: () {
                _controller.toParagraphSelectionPage();
              },
              child: Text(
                '重新选择',
                style: TextStyle(color: Color(0xFF44AF81), fontSize: 14.sp),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return Stack(
      children: [
        Assets.images.chinesePrRecite34Bg.image(
          width: double.infinity,
          height: double.infinity,
          fit: BoxFit.fill,
        ),
        Column(
          children: [
            _buildAppBar(),
            ValueListenableBuilder(
                valueListenable: _controller.pageData,
                builder: (_, pageData, child) {
                  return Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          if (pageData?.segmentType == '1')
                            _buildFullTextRecitation(pageData), //全文背诵
                          _buildParagraph(pageData), //分段背诵
                        ],
                      ),
                    ),
                  );
                }),
          ],
        )
      ],
    );
  }

  Widget _buildTitleView(title) {
    return Padding(
      padding: EdgeInsets.only(bottom: 10.r),
      child: Row(
        children: [
          Assets.images.chinesePrMicrophoneIcon.image(width: 16.r),
          SizedBox(
            width: 5.r,
          ),
          Text(
            title,
            style: ThemeConfig.currentTheme.text17M,
          ),
        ],
      ),
    );
  }

  Widget _buildFullTextRecitation(ReadReciteModel? pageData) {
    if (pageData == null) {
      return Container();
    }
    List<Widget> children = [];
    for (var i = 0;
        i <
            (MScreenUtil.isPad()
                ? 10
                : MScreenUtil.isFoldable()
                    ? 8
                    : 5);
        i++) {
      children.add(_buildPersonItem(pageData.reciteList[i]));
    }
    children.add(
      InkWell(
        onTap: () {
          _controller.toRankingListPage();
        },
        child: Container(
          alignment: Alignment.center,
          width: 30.r,
          child: Text(
            '》',
            style: TextStyle(
              fontSize: 20.sp,
              color: ThemeConfig.currentTheme.colorWhiteGrey4,
            ),
          ),
        ),
      ),
    );

    return Padding(
      padding: EdgeInsets.all(15.r),
      child: Column(
        children: [
          _buildTitleView('全文背诵'),
          Container(
            padding: EdgeInsets.all(10.r),
            decoration: BoxDecoration(
              color: Color(0xFFF9F9F9),
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Column(
              children: [
                _buildListItem(
                  userScore: pageData.reciteVo?.userScore,
                  pageData.reciteVo?.content ?? '',
                  isFullText: true,
                ),
                Padding(
                  padding: EdgeInsets.all(10.r),
                  child: Row(
                    children: [
                      Text(
                        '共',
                        style: ThemeConfig.currentTheme.text14P1,
                      ),
                      Text(
                        '${pageData.reciteCount}',
                        style: TextStyle(
                            fontSize: 14.sp, color: Color(0xFFFE3B14)),
                      ),
                      Text(
                        '人完成课文背诵',
                        style: ThemeConfig.currentTheme.text14P1,
                      ),
                    ],
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: children,
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListItem(String content,
      {int? userScore,
      String? title,
      String? subTitle,
      bool isFullText = false}) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(bottom: 10.r),
                child: Row(
                  children: [
                    if (title != null)
                      Padding(
                        padding: EdgeInsets.only(right: 5.r),
                        child:
                            Text(title, style: ThemeConfig.currentTheme.text14),
                      ),
                    if (subTitle != null)
                      Text(subTitle, style: ThemeConfig.currentTheme.text13P1),
                  ],
                ),
              ),
              ConstrainedBox(
                constraints: BoxConstraints(maxHeight: 80.r),
                child: Padding(
                  padding: EdgeInsets.only(bottom: 10.r),
                  child: SingleChildScrollView(
                    physics: NeverScrollableScrollPhysics(),
                    child: HtmlWidget(
                      '${content}',
                      textStyle: ThemeConfig.currentTheme.text16,
                      customStylesBuilder: (element) {
                        element.attributes['style'] = '''
                                          text-overflow: ellipsis; //当文本溢出时，显示省略号
                                          -webkit-line-clamp: 2; // 设置两行文字溢出
                                        ''';
                        if (element.classes.contains('ql-align-center')) {
                          return {
                            'text-align': 'center',
                          };
                        } else {
                          return null;
                        }
                      },
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        ConstrainedBox(
          constraints: BoxConstraints(maxHeight: 65.r),
          child: Container(
            color: Color(0xFFE5E5E5),
            width: 0.5.r,
            margin: EdgeInsets.symmetric(horizontal: 20.r),
          ),
        ),
        Column(
          children: [
            if (userScore != null)
              Row(
                children: [
                  SizedBox(
                    height: 40.r,
                    child: Text(
                      '$userScore',
                      style:
                          TextStyle(fontSize: 25.sp, color: Color(0xFFFE3B14)),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 5.r),
                    child: Text(
                      '分',
                      style:
                          TextStyle(fontSize: 16.sp, color: Color(0xFF666666)),
                    ),
                  ),
                ],
              ),
            InkWell(
              onTap: () {
                _controller.toRecitePage(isFullText: isFullText);
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 10.r, vertical: 3.r),
                decoration: BoxDecoration(
                  color: Color(0xFF37CE8D),
                  borderRadius: BorderRadius.circular(15.r),
                ),
                child: Text(
                  '去背诵',
                  style: ThemeConfig.currentTheme.text16White,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildParagraph(ReadReciteModel? pageData) {
    if (pageData == null || _controller.userCheckedNum.value == 0) {
      return Container();
    }

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.r),
      child: ValueListenableBuilder(
        valueListenable: _controller.userChecked,
        builder: (_, userChecked, child) {
          List<Widget> children = [
            _buildTitleView('${pageData.segmentType == '1' ? '分段' : '分首'}背诵')
          ];
          int currentNum = 0;
          for (var i = 0; i < userChecked.length; i++) {
            if (userChecked[i] == '1') {
              currentNum++;
              ReciteVoList item = pageData.reciteVoList[i];
              children.add(
                Container(
                  padding: EdgeInsets.all(10.r),
                  margin: EdgeInsets.symmetric(vertical: 10.r),
                  decoration: BoxDecoration(
                    color: Color(0xFFF9F9F9),
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                  child: _buildListItem(
                    item.content ?? '',
                    userScore: item.userScore,
                    title: '第$currentNum段',
                    subTitle: '共${_controller.userCheckedNum.value}段',
                  ),
                ),
              );
            }
          }
          return Column(
            children: children,
          );
        },
      ),
    );
  }

  Widget _buildPersonItem(ReadListElement item) {
    return InkWell(
      onTap: () {
        _controller.rankingItemClick(item);
      },
      child: Column(
        children: [
          item.userPhoto == null
              ? BaseAssets.images.userDefault.image(width: 30.r)
              : BaseNetCacheImage(
                  imageUrl: ImageUtil.getImageUrl(item.userPhoto ?? ''),
                  width: 30.r,
                ),
          SizedBox(height: 5.r),
          SizedBox(
            width: 50.r,
            child: Text(
              '${item.userName ?? ''}',
              style: ThemeConfig.currentTheme.text13,
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
