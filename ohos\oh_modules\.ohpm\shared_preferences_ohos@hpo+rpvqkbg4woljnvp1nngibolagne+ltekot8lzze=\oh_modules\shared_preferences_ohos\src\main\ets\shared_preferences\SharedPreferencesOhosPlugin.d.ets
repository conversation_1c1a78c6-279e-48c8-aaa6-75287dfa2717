// @keepTs
// @ts-nocheck
import { FlutterPlugin, FlutterPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import MessageCodec from '@ohos/flutter_ohos/src/main/ets/plugin/common/MessageCodec';
import { SharedPreferencesApi } from './Messages';
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
export default class SharedPreferencesOhosPlugin implements FlutterPlugin, SharedPreferencesApi {
    private preferences;
    private listEncoder;
    getUniqueClassName(): string;
    constructor();
    setup(y: BinaryMessenger, z: SharedPreferencesApi | null): void;
    remove(x: string): boolean;
    setString(v: string, w: string): Promise<void>;
    setInt(t: string, u: number): Promise<void>;
    put(r: string, s: ESObject): Promise<void>;
    setDouble(p: string, q: number): Promise<void>;
    setStringList(n: string, o: string[]): Promise<void>;
    clear(l: string, m: string[]): Promise<void>;
    filterData(i: [
        string,
        Object
    ], j: string, k: string[]): Map<string, Object>;
    getAll(g: string, h: Array<string>): Promise<Object>;
    getCodec(): MessageCodec<Object>;
    onAttachedToEngine(f: FlutterPluginBinding): void;
    onDetachedFromEngine(e: FlutterPluginBinding): void;
    setBool(c: string, d: boolean): Promise<void>;
    transformPref(b: Object): Object;
}
