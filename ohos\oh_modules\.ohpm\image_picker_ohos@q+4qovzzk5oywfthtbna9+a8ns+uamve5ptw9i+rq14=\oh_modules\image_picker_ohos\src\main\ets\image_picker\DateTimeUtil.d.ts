export default class DateTimeUtil {
    /**
     * 时分秒
     */
    getTime(): string;
    /**
     * 年月日
     */
    getDate(): string;
    /**
     * 日期不足两位补充0
     * @param value-数据值
     */
    fill(e3: number): string;
    /**
     * 年月日格式修饰
     * @param year
     * @param month
     * @param date
     */
    concatDate(b3: number, c3: number, d3: number): string;
    /**
     * Avoid repetition
     * 时分秒格式修饰
     * @param hours
     * @param minutes
     * @param seconds
     */
    concatTime(y2: number, z2: number, a3: number): string;
}
