// @keepTs
// @ts-nocheck
import MethodCall from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodCall';
import { MethodCallHandler, MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import { Connectivity } from './Connectivity';
export default class ConnectivityMethodChannelHandler implements MethodCallHandler {
    private connectivity;
    constructor(j: Connectivity);
    onMethodCall(h: MethodCall, i: MethodResult): void;
    handleCheckResult(g: MethodResult): Promise<void>;
}
