import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lib_base/config/theme_config.dart';
import 'package:lib_base/generated/assets.dart';
import 'package:lib_base/src/utils/asset_utils.dart';
import 'package:lib_base/utils/time_helper.dart';
import 'package:lib_base/utils/ui_util.dart';
import 'package:lib_base/widgets/measure_size_render_object.dart';
import 'package:lib_base/widgets/video/normal_video_player/widgets/play_speed_choose_dialog.dart';
import 'package:video_player/video_player.dart';

class SeekWidgetModel {
  final Widget seekWidget;
  final double width;

  SeekWidgetModel({required this.seekWidget, required this.width});
}

class MNormalVideoProgressIndicator extends StatefulWidget {
  /// The [VideoPlayerController] that actually associates a video with this
  /// widget.
  final VideoPlayerController controller;

  /// The default colors used throughout the indicator.
  ///
  /// See [VideoProgressColors] for default values.
  final VideoProgressColors colors;

  /// When true, the widget will detect touch input and try to seek the video
  /// accordingly. The widget ignores such input when false.
  ///
  /// Defaults to false.
  final bool allowScrubbing;

  /// This allows for visual padding around the progress indicator that can
  /// still detect gestures via [allowScrubbing].
  ///
  /// Defaults to `top: 5.0`.
  final EdgeInsets padding;

  final VoidCallback? onFinish;
  final void Function(double progress, int position)? onProgress;
  final VoidCallback? onFullScreenTap;
  final bool showSpeedControl;
  final bool showFullScreenTap;
  final bool showDlna;
  final SeekWidgetModel? seekWidget;
  final bool showMaxDuration;
  final Widget? playStateWidget;
  final Widget? pauseStateWidget;
  final Color? durationTextColor;

  final ValueChanged<VideoPlayerController>? doPlay;

  const MNormalVideoProgressIndicator(this.controller,
      {super.key,
      required this.allowScrubbing,
      this.colors = const VideoProgressColors(
        playedColor: Colors.white,
      ),
      this.padding = const EdgeInsets.only(top: 5.0, bottom: 5.0),
      this.onFinish,
      this.onProgress,
      this.onFullScreenTap,
      this.showSpeedControl = false,
      this.showFullScreenTap = true,
      this.doPlay,
      this.seekWidget,
      this.showDlna = true,
      this.showMaxDuration = true,
      this.pauseStateWidget,
      this.playStateWidget,
      this.durationTextColor});

  @override
  State<MNormalVideoProgressIndicator> createState() =>
      _MNormalVideoProgressIndicatorState();
}

class _MNormalVideoProgressIndicatorState
    extends State<MNormalVideoProgressIndicator> {
  _MNormalVideoProgressIndicatorState() {
    listener = () {
      if (!mounted) {
        return;
      }
      setState(() {});
    };
  }

  late VoidCallback listener;

  VideoPlayerController get controller => widget.controller;

  VideoProgressColors get colors => widget.colors;

  VideoPlaySpeedModel? _speedModel;

  //进度条的size
  Size? _progressSize;

  bool _hasFinishCalled = false;

  @override
  void initState() {
    super.initState();
    controller.addListener(listener);
    controller.setLooping(false);
  }

  @override
  void deactivate() {
    controller.removeListener(listener);
    super.deactivate();
  }

  @override
  void didUpdateWidget(covariant MNormalVideoProgressIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.controller != widget.controller) {
      oldWidget.controller.removeListener(listener);
      widget.controller.addListener(listener);
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget progressIndicator;
    if (controller.value.isInitialized) {
      final int duration = controller.value.duration.inMilliseconds;
      final int position = controller.value.position.inMilliseconds;

      //当前进度
      if (widget.onProgress != null) {
        widget.onProgress?.call(position / duration, position);
      }

      bool hasFinished = position >= duration - 500;

      if (hasFinished && !_hasFinishCalled) {
        _hasFinishCalled = true;
        widget.onFinish?.call();
      }
      if (position == 0) {
        _hasFinishCalled = false;
      }

      int maxBuffering = 0;
      for (final DurationRange range in controller.value.buffered) {
        final int end = range.end.inMilliseconds;
        if (end > maxBuffering) {
          maxBuffering = end;
        }
      }
      double left = ((position / duration) * (_progressSize?.width ?? 0) -
          (widget.seekWidget?.width ?? 0) / 2);
      progressIndicator = Row(
        children: [
          InkWell(
            onTap: () {
              if (controller.value.isPlaying) {
                controller.pause();
              } else {
                _hasFinishCalled = false;
                if (widget.doPlay != null) {
                  widget.doPlay!.call(controller);
                } else {
                  controller.play();
                }
              }
            },
            child: Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: (controller.value.isPlaying
                      ? widget.pauseStateWidget
                      : widget.playStateWidget) ??
                  Image.asset(
                      AssetsUtils.wrapAsset(controller.value.isPlaying
                          ? ImageAssets.imagesIcPlayerPause
                          : ImageAssets.imagesIcPlayerStart),
                      width: 24.r,
                      gaplessPlayback: true),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: Text(
              TimeFormatHelper.parseDurationToMSString(
                  controller.value.position),
              style: ThemeConfig.currentTheme.text12.copyWith(
                  color: widget.durationTextColor ??
                      ThemeConfig.currentTheme.colorWhite),
            ),
          ),
          Expanded(
              child: MeasureSize(
            onChange: (Size size) {
              _progressSize = size;
            },
            child: Stack(
              alignment: Alignment.centerLeft,
              fit: StackFit.passthrough,
              children: <Widget>[
                LinearProgressIndicator(
                  value: maxBuffering / duration,
                  valueColor:
                      AlwaysStoppedAnimation<Color>(colors.bufferedColor),
                  backgroundColor: colors.backgroundColor,
                ),
                LinearProgressIndicator(
                  value: position / duration,
                  valueColor: AlwaysStoppedAnimation<Color>(colors.playedColor),
                  backgroundColor: Colors.transparent,
                ),
                (widget.seekWidget != null && _progressSize != null && left > 0)
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Padding(
                              padding: EdgeInsets.only(left: left),
                              child: widget.seekWidget!.seekWidget),
                        ],
                      )
                    : const SizedBox.shrink(),
              ],
            ),
          )),
          if (widget.showMaxDuration)
            Padding(
              padding: const EdgeInsets.only(left: 8.0),
              child: Text(
                TimeFormatHelper.parseDurationToMSString(
                    controller.value.duration),
                style: ThemeConfig.currentTheme.text12.copyWith(
                    color: widget.durationTextColor ??
                        ThemeConfig.currentTheme.colorWhite),
              ),
            ),
          //倍速播放
          Offstage(
            offstage: !widget.showSpeedControl,
            child: InkWell(
                onTap: () {
                  showSmartDialog(
                          PlaySpeedChooseDialog(
                            speedModel: _speedModel,
                          ),
                          alignment: Alignment.topRight)
                      .then((value) {
                    if (value != null &&
                        value is VideoPlaySpeedModel &&
                        value.speed != _speedModel?.speed) {
                      _speedModel = value;
                      controller.setPlaybackSpeed(_speedModel!.speed);
                      setState(() {});
                    }
                  });
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Text(
                    _speedModel?.name ?? "1.0X",
                    style: ThemeConfig.currentTheme.text15White,
                  ),
                )),
          ),
          widget.showFullScreenTap
              ? InkWell(
                  onTap: widget.onFullScreenTap,
                  child: Padding(
                    padding: const EdgeInsets.only(left: 8.0),
                    child: Image.asset(
                      AssetsUtils.wrapAsset(ImageAssets.imagesIconScreenFull),
                      width: 24.r,
                    ),
                  ),
                )
              : const SizedBox.shrink(),
          widget.showDlna
              ? InkWell(
                  onTap: () {
                    showToast("敬请期待");
                  },
                  child: Padding(
                    padding: const EdgeInsets.only(left: 8.0),
                    child: Image.asset(
                      AssetsUtils.wrapAsset(ImageAssets.imagesIconDlna),
                      width: 24.r,
                    ),
                  ),
                )
              : const SizedBox.shrink(),
        ],
      );
    } else {
      progressIndicator = LinearProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(colors.playedColor),
        backgroundColor: colors.backgroundColor,
      );
    }
    final Widget paddedProgressIndicator = Padding(
      padding: widget.padding,
      child: progressIndicator,
    );
    if (widget.allowScrubbing) {
      return VideoScrubber(
        controller: controller,
        child: paddedProgressIndicator,
      );
    } else {
      return paddedProgressIndicator;
    }
  }
}
