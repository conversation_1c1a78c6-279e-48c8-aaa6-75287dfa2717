// @keepTs
// @ts-nocheck
import WebMessagePortCompatExt from './WebMessagePortCompatExt';
import List from "@ohos.util.List";
import { Any } from '@ohos/flutter_ohos';
import web_webview from '@ohos.web.webview';
export default class WebMessageCompatExt {
    private data;
    private type;
    private ports;
    constructor(n15: Any, o15: number, p15: List<WebMessagePortCompatExt> | null);
    static fromMapWebMessageCompat(m15: web_webview.WebMessageExt): WebMessageCompatExt;
    static fromMap(l15: Map<string, Any> | null): WebMessageCompatExt | null;
    toMap(): Map<string, Any>;
    getData(): Any;
    setData(k15: Any): void;
    getType(): number;
    setType(j15: number): void;
    getPorts(): List<WebMessagePortCompatExt>;
    setPorts(i15: List<WebMessagePortCompatExt>): void;
    equals(h15: Any): boolean;
    toString(): string;
}
